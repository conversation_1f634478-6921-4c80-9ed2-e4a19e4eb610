<!DOCTYPE html>
<html lang="en" data-bs-theme="light">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>{% block title %}Admin Dashboard{% endblock %}</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.2/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.2/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <style>
        /* CVBIOLABS ADMIN PANEL - PROFESSIONAL DESIGN SYSTEM */
        :root {
            /* Home Page Color System - Exact Match */
            --primary-orange: #f58220;
            --deep-blue: #003865;
            --bright-blue: #007dc5;
            --light-bg: #f0f7fb;
            --white: #ffffff;
            --text-dark: #1a1a1a;
            --text-light: #6b7280;
            --success: #10b981;
            --warning: #ED6C02;
            --danger: #D32F2F;
            --info: #0288D1;

            /* Professional Gradients - No Glossy Effects */
            --gradient-primary: linear-gradient(135deg, var(--deep-blue) 0%, var(--bright-blue) 100%);
            --gradient-accent: linear-gradient(135deg, var(--primary-orange) 0%, #ff6b35 100%);

            /* Professional Shadows - Subtle and Clean */
            --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
            --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1);
            --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1);
            --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1);

            /* UI Variables - Professional Design */
            --border-radius: 16px;
            --card-radius: 12px;
            --button-radius: 8px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        html {
            scroll-behavior: smooth;
        }

        body {
            background: #f6f8fa;
            min-height: 100vh;
            display: flex;
            font-family: 'Inter', sans-serif;
            color: #2d3748;
            line-height: 1.6;
            overflow-x: hidden;
        }

        /* Comfortable Sidebar - Neutral Design */
        .sidebar {
            position: fixed;
            top: 0;
            bottom: 0;
            left: 0;
            z-index: 100;
            width: 280px;
            background: #fafbfc;
            border-right: 1px solid #e2e8f0;
            display: flex;
            flex-direction: column;
            height: 100vh;
            overflow: hidden;
            box-shadow: none;
        }

        /* Comfortable Sidebar Header */
        .sidebar-header {
            padding: 1.5rem 1.5rem 1rem;
            text-align: center;
            border-bottom: 1px solid #e2e8f0;
            margin-bottom: 1rem;
            flex-shrink: 0;
            background: #fafbfc;
        }

        .sidebar-header img {
            width: 60px;
            height: 60px;
            border-radius: 6px;
            margin-bottom: 0.75rem;
            filter: none;
        }

        .sidebar-header h5 {
            font-weight: 500;
            letter-spacing: 0;
            color: #2d3748;
            margin: 0;
            font-size: 1rem;
            font-family: 'Inter', sans-serif;
            background: none;
            -webkit-background-clip: unset;
            -webkit-text-fill-color: unset;
            background-clip: unset;
        }

        /* Professional Sidebar Navigation */
        .sidebar-sticky {
            flex: 1;
            overflow-y: auto;
            padding: 0 1.5rem;
            margin-bottom: 1rem;
        }

        .sidebar-sticky::-webkit-scrollbar {
            width: 4px;
        }

        .sidebar-sticky::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.05);
        }

        .sidebar-sticky::-webkit-scrollbar-thumb {
            background: var(--primary-orange);
            border-radius: 2px;
        }

        .sidebar-sticky::-webkit-scrollbar-thumb:hover {
            background: var(--deep-blue);
        }

        .nav-link {
            padding: 0.875rem 1.25rem;
            display: flex;
            align-items: center;
            gap: 0.875rem;
            color: #4a5568;
            border-radius: 4px;
            margin-bottom: 0.25rem;
            transition: none;
            font-weight: 400;
            font-size: 0.9rem;
            text-decoration: none;
            border: 1px solid transparent;
        }

        .nav-link i {
            width: 18px;
            text-align: center;
            font-size: 1rem;
            color: #4a5568;
            transition: none;
        }

        .nav-link:hover {
            background: #f1f3f5;
            color: #2d3748;
            transform: none;
            border-color: transparent;
        }

        .nav-link:hover i {
            color: #2d3748;
        }

        .nav-link.active {
            background: #f1f3f5;
            color: #2d3748;
            box-shadow: none;
            border-color: #e2e8f0;
        }

        .nav-link.active i {
            color: #2d3748;
        }

        /* Professional Sidebar Footer */
        .sidebar-footer {
            padding: 1.5rem;
            border-top: 1px solid rgba(0, 0, 0, 0.08);
            flex-shrink: 0;
            background: var(--white);
        }

        /* Comfortable Main Content Area */
        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: 0;
            transition: none;
            min-height: 100vh;
            background: #f6f8fa;
        }

        /* Comfortable Top Bar */
        .top-bar {
            background: #fafbfc;
            padding: 1rem 2rem;
            border-bottom: 1px solid #e2e8f0;
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0;
        }

        .top-bar h1 {
            font-weight: 500;
            font-size: 1.375rem;
            color: #2d3748;
            font-family: 'Inter', sans-serif;
            margin: 0;
        }

        /* Professional Card Design - Home Page Style */
        .card {
            background: var(--white);
            border: 1px solid rgba(0, 0, 0, 0.05);
            border-radius: var(--card-radius);
            box-shadow: var(--shadow-sm);
            margin-bottom: 1.5rem;
            transition: var(--transition);
            overflow: hidden;
        }

        .card:hover {
            transform: translateY(-3px);
            box-shadow: var(--shadow-md);
        }

        /* Professional Button Design - Home Page Style */
        .btn {
            padding: 0.75rem 1.5rem;
            font-weight: 600;
            border-radius: var(--button-radius);
            transition: var(--transition);
            border: none;
            display: inline-flex;
            align-items: center;
            gap: 0.5rem;
            font-size: 0.9rem;
            text-decoration: none;
            cursor: pointer;
        }

        .btn-primary {
            background: var(--gradient-accent);
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .btn-primary:hover {
            background: var(--gradient-accent);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
            color: white;
        }

        .btn-secondary {
            background: var(--gradient-primary);
            color: white;
            box-shadow: var(--shadow-sm);
        }

        .btn-secondary:hover {
            background: var(--gradient-primary);
            transform: translateY(-1px);
            box-shadow: var(--shadow-md);
            color: white;
        }

        .btn-danger {
            background-color: var(--danger);
            border-color: var(--danger);
        }

        .btn-danger:hover {
            background-color: #c82333;
            border-color: #bd2130;
            transform: translateY(-1px);
        }

        /* Professional Table Design */
        .table {
            color: var(--text-dark);
            font-size: 0.95rem;
            border-collapse: separate;
            border-spacing: 0;
        }

        .table th {
            font-weight: 600;
            background: var(--light-bg);
            padding: 1rem 1.5rem;
            color: var(--text-dark);
            border: none;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            font-size: 0.85rem;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .table td {
            padding: 1rem 1.5rem;
            vertical-align: middle;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
            background: var(--white);
        }

        .table tbody tr:hover {
            background: rgba(245, 130, 32, 0.02);
        }

        /* Professional Badge Design */
        .badge {
            padding: 0.4rem 0.8rem;
            font-weight: 600;
            border-radius: var(--button-radius);
            font-size: 0.75rem;
            text-transform: uppercase;
            letter-spacing: 0.3px;
        }

        .badge.bg-primary { background: var(--deep-blue) !important; }
        .badge.bg-secondary { background: var(--primary-orange) !important; }
        .badge.bg-success { background: var(--success) !important; }
        .badge.bg-warning { background: var(--warning) !important; }
        .badge.bg-danger { background: var(--danger) !important; }
        .badge.bg-info { background: var(--info) !important; }

        /* Professional Form Design */
        .form-control, .form-select {
            padding: 0.75rem 1rem;
            border-radius: var(--button-radius);
            border: 1px solid rgba(0, 0, 0, 0.1);
            font-size: 0.95rem;
            transition: var(--transition);
            background: var(--white);
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-orange);
            box-shadow: 0 0 0 0.2rem rgba(245, 130, 32, 0.1);
            outline: none;
        }

        /* Professional Modal Design */
        .modal-content {
            border-radius: var(--card-radius);
            border: none;
            box-shadow: var(--shadow-xl);
            overflow: hidden;
        }

        .modal-header {
            background: var(--gradient-primary);
            color: var(--white);
            padding: 1.5rem;
            border-bottom: none;
        }

        .modal-header .btn-close {
            color: var(--white);
            opacity: 0.8;
            filter: invert(1);
        }

        .modal-body {
            padding: 2rem;
            background: var(--white);
        }

        .modal-footer {
            padding: 1.5rem 2rem;
            border-top: 1px solid rgba(0, 0, 0, 0.05);
            background: var(--light-bg);
        }

        /* Card Background Colors */
        .bg-primary { background: var(--gradient-primary) !important; }
        .bg-secondary { background: var(--gradient-accent) !important; }
        .bg-success { background: linear-gradient(135deg, var(--success) 0%, #059669 100%) !important; }
        .bg-warning { background: var(--gradient-accent) !important; }
        .bg-danger { background-color: var(--danger) !important; }
        .bg-info { background: linear-gradient(135deg, var(--bright-blue) 0%, #0284c7 100%) !important; }

        /* Text Colors */
        .text-primary { color: var(--deep-blue) !important; }
        .text-secondary { color: var(--primary-orange) !important; }
        .text-success { color: var(--success) !important; }
        .text-warning { color: var(--warning) !important; }
        .text-danger { color: var(--danger) !important; }
        .text-info { color: var(--info) !important; }

        /* Loading animation */
        .loading {
            position: relative;
            overflow: hidden;
        }

        .loading::after {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            animation: loading 1.5s infinite;
        }

        @keyframes loading {
            0% { transform: translateX(-100%); }
            100% { transform: translateX(100%); }
        }

        /* Custom scrollbar */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: var(--light-bg);
        }

        ::-webkit-scrollbar-thumb {
            background: var(--primary-orange);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: var(--deep-blue);
        }

        /* Professional Mobile Responsiveness */
        @media (max-width: 1200px) {
            .main-content {
                margin-left: 280px;
                padding: 1.5rem;
            }

            .top-bar {
                padding: 1.25rem 1.5rem;
            }

            .top-bar h1 {
                font-size: 1.5rem;
            }
        }

        @media (max-width: 1024px) {
            .main-content {
                padding: 1rem;
            }

            .top-bar {
                padding: 1rem;
                flex-direction: column;
                gap: 1rem;
                align-items: stretch;
            }

            .top-bar h1 {
                font-size: 1.25rem;
                text-align: center;
            }

            .sidebar {
                width: 260px;
            }

            .main-content {
                margin-left: 260px;
            }
        }

        /* Mobile Backdrop */
        .mobile-backdrop {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 1040;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .mobile-backdrop.show {
            display: block;
            opacity: 1;
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                width: 100%;
                max-width: 320px;
                z-index: 1050;
                box-shadow: var(--shadow-xl);
                background: var(--white);
            }

            .sidebar.show {
                transform: translateX(0);
                animation: slideInLeft 0.3s ease;
            }

            .main-content {
                margin-left: 0;
                padding: 1rem;
                width: 100%;
            }

            .top-bar {
                margin-top: 0;
                padding: 1rem;
                flex-direction: row;
                align-items: center;
                gap: 1rem;
                border-radius: var(--button-radius);
            }

            .top-bar h1 {
                font-size: 1.2rem;
                flex: 1;
                text-align: left;
                margin: 0;
            }

            .top-bar .d-flex:last-child {
                flex-direction: row;
                gap: 0.5rem;
            }

            .top-bar .btn {
                padding: 0.5rem 1rem;
                font-size: 0.8rem;
                min-width: auto;
            }

            .btn {
                padding: 0.5rem 1rem;
                font-size: 0.8rem;
                border-radius: 8px;
            }

            .card {
                margin-bottom: 1rem;
                border-radius: 12px;
            }

            .card-body {
                padding: 1rem;
            }

            /* Enhanced table responsiveness */
            .table-responsive {
                border-radius: 8px;
                font-size: 0.8rem;
            }

            .table th,
            .table td {
                padding: 0.5rem 0.75rem;
                font-size: 0.8rem;
            }

            /* Modal improvements for mobile */
            .modal-dialog {
                margin: 0.5rem;
                max-width: calc(100% - 1rem);
            }

            .modal-content {
                border-radius: 12px;
            }

            .modal-header {
                padding: 1rem;
            }

            .modal-body {
                padding: 1rem;
            }

            .modal-footer {
                padding: 1rem;
                flex-direction: column;
                gap: 0.5rem;
            }

            .modal-footer .btn {
                width: 100%;
                margin: 0;
            }

            /* Form improvements */
            .form-control,
            .form-select {
                padding: 0.75rem;
                font-size: 0.9rem;
                border-radius: 8px;
            }

            /* Badge improvements */
            .badge {
                padding: 0.25rem 0.5rem;
                font-size: 0.7rem;
            }
        }

        @media (max-width: 480px) {
            .main-content {
                padding: 0.75rem;
            }

            .top-bar {
                padding: 0.75rem;
                flex-direction: column;
                gap: 1rem;
                margin-bottom: 1rem;
                border-radius: var(--button-radius);
            }

            .top-bar h1 {
                font-size: 1.1rem;
                text-align: center;
                margin: 0;
            }

            .top-bar .d-flex:first-child {
                width: 100%;
                justify-content: space-between;
                align-items: center;
            }

            .top-bar .d-flex:last-child {
                width: 100%;
                flex-direction: row;
                justify-content: center;
                gap: 0.75rem;
            }

            .sidebar {
                width: 100%;
            }

            .sidebar-header {
                padding: 1rem;
            }

            .sidebar-header img {
                width: 60px;
                height: 60px;
            }

            .sidebar-header h5 {
                font-size: 1rem;
            }

            .nav-link {
                padding: 0.875rem 1rem;
                font-size: 0.9rem;
            }

            .nav-link i {
                font-size: 1rem;
            }

            .btn {
                padding: 0.5rem 0.75rem;
                font-size: 0.75rem;
                border-radius: 6px;
            }

            .top-bar .btn {
                padding: 0.4rem 0.6rem;
                font-size: 0.7rem;
                min-width: 70px;
            }

            /* Stats cards for mobile */
            .stat-card .card-body {
                padding: 0.75rem;
                text-align: center;
            }

            .stat-card h2 {
                font-size: 1.5rem;
            }

            .stat-card h5 {
                font-size: 0.75rem;
                margin-bottom: 0.5rem;
            }

            /* Table improvements for very small screens */
            .table-responsive {
                font-size: 0.7rem;
            }

            .table th,
            .table td {
                padding: 0.4rem 0.5rem;
                font-size: 0.7rem;
            }

            /* Hide less important table columns on very small screens */
            .table th:nth-child(n+4),
            .table td:nth-child(n+4) {
                display: none;
            }

            /* Form improvements for very small screens */
            .form-control,
            .form-select {
                padding: 0.6rem;
                font-size: 0.85rem;
            }

            .form-label {
                font-size: 0.85rem;
                font-weight: 600;
            }

            /* Modal improvements for very small screens */
            .modal-dialog {
                margin: 0.25rem;
                max-width: calc(100% - 0.5rem);
            }

            .modal-header h5 {
                font-size: 1rem;
            }

            .modal-body {
                padding: 0.75rem;
            }

            .modal-footer {
                padding: 0.75rem;
            }
        }

        /* Animation for sidebar slide-in */
        @keyframes slideInLeft {
            from {
                transform: translateX(-100%);
            }
            to {
                transform: translateX(0);
            }
        }

        /* Touch-friendly improvements */
        @media (hover: none) and (pointer: coarse) {
            .btn {
                min-height: 44px;
                touch-action: manipulation;
            }

            .nav-link {
                min-height: 44px;
                touch-action: manipulation;
            }

            .table td {
                min-height: 44px;
            }

            .form-control,
            .form-select {
                min-height: 44px;
            }
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Mobile Backdrop -->
    <div class="mobile-backdrop" id="mobileBackdrop"></div>

    <!-- Professional Sidebar -->
    <nav class="sidebar">
        <div class="sidebar-header">
            <img src="{{ url_for('static', filename='images/CV.png') }}" alt="CVBIO LABS">
            <h5>Admin Panel</h5>
        </div>
        <div class="sidebar-sticky">
            <ul class="nav flex-column">
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'admin_dashboard' %}active{% endif %}" href="/admin/dashboard">
                        <i class="fas fa-home"></i>
                        <span>Dashboard</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'admin_analytics' %}active{% endif %}" href="/admin/analytics">
                        <i class="fas fa-chart-line"></i>
                        <span>Analytics</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'admin_payments' %}active{% endif %}" href="/admin/payments">
                        <i class="fas fa-credit-card"></i>
                        <span>Payments</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'admin_users' %}active{% endif %}" href="/admin/users">
                        <i class="fas fa-users"></i>
                        <span>User Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'admin.admin_tests' %}active{% endif %}" href="/admin/tests">
                        <i class="fas fa-flask"></i>
                        <span>Test Management</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'admin_reset_password' %}active{% endif %}" href="/admin/reset-password">
                        <i class="fas fa-key"></i>
                        <span>Password Reset</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'admin_discounts' %}active{% endif %}" href="/admin/discounts">
                        <i class="fas fa-tag"></i>
                        <span>Discounts Chance</span>
                    </a>
                </li>
                <li class="nav-item">
                    <a class="nav-link {% if request.endpoint == 'admin_advertisements' %}active{% endif %}" href="/admin/advertisements">
                        <i class="fas fa-bullhorn"></i>
                        <span>Advertisement Management</span>
                    </a>
                </li>
            </ul>
        </div>
        <div class="sidebar-footer">
            <a href="{{ url_for('admin.admin_logout') }}" class="btn btn-danger w-100">
                <i class="fas fa-sign-out-alt me-2"></i>Logout
            </a>
        </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
        <div class="top-bar">
            <div class="d-flex align-items-center">
                <button class="btn btn-primary d-md-none me-3" id="sidebarToggle">
                    <i class="fas fa-bars"></i>
                </button>
                <h1>{% block page_title %}{% endblock %}</h1>
            </div>
            <div class="d-flex align-items-center">
                <a href="/admin/dashboard" class="btn btn-secondary me-2">
                    <i class="fas fa-arrow-left me-2"></i>Back
                </a>
                <a href="{{ url_for('admin.admin_logout') }}" class="btn btn-danger">
                    <i class="fas fa-sign-out-alt me-2"></i>Logout
                </a>
            </div>
        </div>

        {% block content %}{% endblock %}
    </main>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/bootstrap/5.3.2/js/bootstrap.bundle.min.js"></script>
    <script>
        // Toggle sidebar on mobile
        document.getElementById('sidebarToggle')?.addEventListener('click', function() {
            const sidebar = document.querySelector('.sidebar');
            const backdrop = document.getElementById('mobileBackdrop');

            sidebar.classList.toggle('show');
            backdrop.classList.toggle('show');
        });

        // Close sidebar when clicking backdrop
        document.getElementById('mobileBackdrop')?.addEventListener('click', function() {
            const sidebar = document.querySelector('.sidebar');
            const backdrop = document.getElementById('mobileBackdrop');

            sidebar.classList.remove('show');
            backdrop.classList.remove('show');
        });

        // Close sidebar when clicking outside on mobile
        document.addEventListener('click', function(event) {
            const sidebar = document.querySelector('.sidebar');
            const sidebarToggle = document.getElementById('sidebarToggle');
            const backdrop = document.getElementById('mobileBackdrop');

            if (window.innerWidth < 768 &&
                !sidebar.contains(event.target) &&
                !sidebarToggle?.contains(event.target)) {
                sidebar.classList.remove('show');
                backdrop.classList.remove('show');
            }
        });

        // Handle window resize
        window.addEventListener('resize', function() {
            if (window.innerWidth >= 768) {
                const sidebar = document.querySelector('.sidebar');
                const backdrop = document.getElementById('mobileBackdrop');

                sidebar.classList.remove('show');
                backdrop.classList.remove('show');
            }
        });

        // Add smooth scrolling for better mobile experience
        document.documentElement.style.scrollBehavior = 'smooth';
    </script>
    {% block extra_js %}{% endblock %}
</body>
</html>