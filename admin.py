from flask import Flask, render_template, request, jsonify, send_file, session, redirect, url_for, flash, Blueprint
from flask_wtf.csrf import CSRFProtect, generate_csrf, CSRFError
from flask_session import Session
import mysql.connector
from mysql.connector import Error
from datetime import datetime, timedelta, date
from pymysql.cursors import DictCursor
import io
from openpyxl import Workbook
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.units import inch
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, Spacer, PageBreak
from reportlab.platypus.frames import Frame
from reportlab.platypus.doctemplate import PageTemplate, BaseDocTemplate
from reportlab.lib import colors
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT
from PIL import Image, ImageDraw, ImageFont
import secrets
import string
import hashlib
import json
from decimal import Decimal
import traceback
from functools import wraps
import bcrypt
from dotenv import load_dotenv
import os
from flask_mail import Mail, Message, Connection
import redis
from flask_limiter import Limiter
from flask_limiter.util import get_remote_address
import smtplib
import logging

# Suppress smtplib and Flask-Mail debug output by configuring their loggers
logging.getLogger('smtplib').setLevel(logging.CRITICAL)
logging.getLogger('flask_mail').setLevel(logging.CRITICAL)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(name)s %(message)s',
    handlers=[
        logging.FileHandler("admin.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Load environment variables
load_dotenv()

# Create Blueprint
admin_bp = Blueprint('admin', __name__, url_prefix='/admin')

app = Flask(__name__)

# Secure environment variable enforcement
app.secret_key = os.getenv('SECRET_KEY')
if not app.secret_key:
    raise RuntimeError('SECRET_KEY must be set in .env for production!')

redis_url = os.getenv('REDIS_URL')
if not redis_url:
    raise RuntimeError('REDIS_URL must be set in .env for production!')
app.config['SESSION_REDIS'] = redis.from_url(redis_url)

# Configure Redis session
app.config['SESSION_TYPE'] = 'redis'
app.config['SESSION_COOKIE_SECURE'] = True
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(days=1)
Session(app)

# Database configuration (no insecure defaults)
DB_CONFIG = {
    'host': os.getenv('DB_HOST'),
    'user': os.getenv('DB_USER'),
    'password': os.getenv('DB_PASSWORD'),
    'database': os.getenv('DB_NAME'),
    'charset': os.getenv('DB_CHARSET', 'utf8mb4')
}
for key in ['host', 'user', 'password', 'database']:
    if not DB_CONFIG[key]:
        raise RuntimeError(f"DB_{key.upper()} must be set in .env for production!")

# Configure Flask-Mail
app.config['MAIL_SERVER'] = os.getenv('MAIL_SERVER', 'smtpout.secureserver.net')
app.config['MAIL_PORT'] = int(os.getenv('MAIL_PORT', 465))
app.config['MAIL_USE_TLS'] = os.getenv('MAIL_USE_TLS', 'False').lower() == 'true'
app.config['MAIL_USE_SSL'] = os.getenv('MAIL_USE_SSL', 'True').lower() == 'true'
app.config['MAIL_USERNAME'] = os.getenv('MAIL_USERNAME')
app.config['MAIL_PASSWORD'] = os.getenv('MAIL_PASSWORD')
app.config['MAIL_DEFAULT_SENDER'] = os.getenv('MAIL_USERNAME')
app.config['MAIL_DEBUG'] = False  # Explicitly disable Flask-Mail debug output
app.config['MAIL_SUPPRESS_SEND'] = False

if not app.config['MAIL_USERNAME'] or not app.config['MAIL_PASSWORD']:
    logger.warning("Email credentials not set in environment variables!")

# Patch Flask-Mail Connection to ensure no debug output
def no_debug_connection(self, mail):
    logger.info("Applying no_debug_connection patch for Flask-Mail")
    self.host = smtplib.SMTP(
        mail.server, mail.port, timeout=mail.timeout
    )
    self.host.set_debuglevel(0)  # Explicitly disable debug output
    if mail.use_tls:
        self.host.starttls()
    if mail.username and mail.password:
        self.host.login(mail.username, mail.password)

Connection._init_ = no_debug_connection

# Initialize Flask-Mail
mail = Mail(app)


limiter = Limiter(
    get_remote_address,
    app=app,
    default_limits=["200 per day", "50 per hour"]
)

logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s %(levelname)s %(name)s %(message)s',
    handlers=[
        logging.FileHandler("admin.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

@admin_bp.context_processor
def inject_csrf_token():
    return dict(csrf_token=generate_csrf())

@admin_bp.errorhandler(CSRFError)
def handle_csrf_error(e):
    return jsonify({
        'error': 'CSRF token missing or invalid'
    }), 400

# CSRF exemption decorator for admin API routes
def csrf_exempt(f):
    """Exempt specific admin routes from CSRF protection"""
    setattr(f, '_csrf_exempt', True)
    return f

@app.errorhandler(429)
def ratelimit_handler(e):
    if request.accept_mimetypes.accept_json:
        return jsonify({
            "error": "Too many requests. Please try after sometime."
        }), 429
    return render_template("rate_limit.html", message="Too many requests. Please try after sometime."), 429 

# Admin credentials from environment variables
ADMIN_USERNAME = os.getenv('ADMIN_USERNAME')
ADMIN_PASSWORD = os.getenv('ADMIN_PASSWORD')

# Login required decorator
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'user_id' not in session:
            return redirect(url_for('login'))  # Changed from 'admin.login' to 'login'
        return f(*args, **kwargs)
    return decorated_function


def role_required(*roles):
    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            user_role = session.get('user_role') or session.get('role')
            if user_role not in roles:
                flash('You do not have permission to access this page.', 'danger')
                return redirect(url_for('login'))
            return f(*args, **kwargs)
        return decorated_function
    return decorator

def log_audit_action(user_id, username, action, details, conn):
    try:
        with conn.cursor() as cursor:
            cursor.execute(
                "INSERT INTO audit_logs (user_id, username, action, details) VALUES (%s, %s, %s, %s)",
                (user_id, username, action, details)
            )
            conn.commit()
    except Exception as e:
        logger.error("Failed to write audit log: %s", str(e), exc_info=True)

# Custom JSON encoder to handle Decimal and date objects
class DecimalEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, Decimal):
            return float(obj)
        if isinstance(obj, (datetime, date)):
            return obj.isoformat()
        return super(DecimalEncoder, self).default(obj)

app.json_encoder = DecimalEncoder

# Database configuration


def get_db_connection():
    try:
        conn = mysql.connector.connect(**DB_CONFIG)
        return conn
    except Error as e:
        print(f"Database connection error: {e}")
        return None


@admin_bp.app_template_filter('status_badge')  # Add this new name
@admin_bp.app_template_filter('status_badge_color')  # Keep existing name
@admin_bp.app_template_filter('get_badge_color')     # Keep existing name
def get_badge_color(status):
    """Returns a Bootstrap badge color based on status."""
    status_colors = {
        "active": "success",
        "inactive": "secondary", 
        "pending": "warning",
        "banned": "danger",
        "paid": "success",
        "failed": "danger",
        "expired": "secondary",
        "used": "warning"
    }
    return status_colors.get(status.lower(), "dark")

@admin_bp.app_template_filter('user_badge_color')
def user_badge_color(user_type):
    """Returns a Bootstrap badge color based on user type."""
    colors = {
        'Doctor': 'primary',
        'Receptionist': 'success',
        'Patient': 'info',
        'Pickup Agent': 'warning'
    }
    return colors.get(user_type, 'secondary')


# Dashboard Route
@admin_bp.route('/dashboard')
@login_required
@role_required('admin')
def admin_dashboard():
    conn = get_db_connection()
    if not conn:
        return "Database connection failed", 500

    try:
        with conn.cursor(dictionary=True) as cursor:
            # Total Bookings
            cursor.execute("SELECT COALESCE(COUNT(*), 0) as total FROM bookings")
            total_bookings = cursor.fetchone()['total']
            
            # Revenue
            cursor.execute("SELECT COALESCE(SUM(amount), 0) as revenue FROM payments WHERE payment_status = 'paid'")
            revenue = cursor.fetchone()['revenue']
            
            # Active Users
            cursor.execute("SELECT COALESCE(COUNT(*), 0) as total FROM users WHERE status = 1")
            active_users = cursor.fetchone()['total']
            
            # Pending Bookings
            cursor.execute("SELECT COALESCE(COUNT(*), 0) as total FROM bookings WHERE booking_status = 'pending'")
            pending_bookings = cursor.fetchone()['total']
            
            # Recent Bookings Query
            cursor.execute("""
                SELECT 
                    b.id,
                    COALESCE(CONCAT(up.first_name, ' ', up.last_name), 'Unknown') AS customer,
                    COALESCE(td.TestName, 'No Test') AS service,
                    DATE_FORMAT(b.booking_date, '%Y-%m-%d') as date,
                    COALESCE(b.booking_status, 'pending') AS status
                FROM bookings b
                LEFT JOIN users u ON b.user_id = u.id
                LEFT JOIN user_profiles up ON u.id = up.user_id
                LEFT JOIN testdetails td ON b.test_id = td.SrNo
                ORDER BY b.booking_date DESC
                LIMIT 10
            """)
            recent_bookings = cursor.fetchall()

            # Convert Decimal to float for JSON serialization
            revenue = float(revenue) if revenue else 0.0

            return render_template('ADMIN/admindashboard.html',
                                total_bookings=total_bookings,
                                revenue=revenue,
                                active_users=active_users,
                                pending_bookings=pending_bookings,
                                recent_bookings=recent_bookings)

    except Exception as e:
         logger.error("Database Error: %s", str(e), exc_info=True)
         return "Database error", 500
    finally:
        conn.close()

# Create Booking Route
@admin_bp.route('/bookings/create', methods=['POST'])
@login_required
@role_required('admin')
def create_booking():
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        form_data = request.form
        customer = form_data.get('customer')
        service = form_data.get('service')
        booking_date = form_data.get('booking_date')
        status = form_data.get('status')

        if not all([customer, service, booking_date, status]):
            return jsonify({'error': 'All fields are required'}), 400

        with conn.cursor(dictionary=True) as cursor:
            # Fetch user_id based on username
            cursor.execute("SELECT id FROM users WHERE username = %s", (customer,))
            user = cursor.fetchone()
            user_id = user['id'] if user else None

            # Fetch test_id based on TestName
            cursor.execute("SELECT SrNo FROM testdetails WHERE TestName = %s", (service,))
            test = cursor.fetchone()
            test_id = test['SrNo'] if test else None

            if not user_id or not test_id:
                return jsonify({'error': 'Invalid customer or service name'}), 400

            cursor.execute("""
                INSERT INTO bookings (user_id, test_id, booking_date, status)
                VALUES (%s, %s, %s, %s)
            """, (user_id, test_id, booking_date, status))
            conn.commit()

        return jsonify({'message': 'Booking created successfully!'})
    except Exception as e:
        conn.rollback()
        logger.error("Error creating booking: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

# Update Booking Route
@admin_bp.route('/bookings/update', methods=['POST'])
@login_required
@role_required('admin')
def update_booking():
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        form_data = request.form
        booking_id = form_data.get('id')
        customer = form_data.get('customer')
        service = form_data.get('service')
        booking_date = form_data.get('booking_date')
        status = form_data.get('status')

        if not all([booking_id, customer, service, booking_date, status]):
            return jsonify({'error': 'All fields are required'}), 400

        with conn.cursor(dictionary=True) as cursor:
            # Fetch user_id based on username
            cursor.execute("SELECT id FROM users WHERE username = %s", (customer,))
            user = cursor.fetchone()
            user_id = user['id'] if user else None

            # Fetch test_id based on TestName
            cursor.execute("SELECT SrNo FROM testdetails WHERE TestName = %s", (service,))
            test = cursor.fetchone()
            test_id = test['SrNo'] if test else None

            if not user_id or not test_id:
                return jsonify({'error': 'Invalid customer or service name'}), 400

            cursor.execute("""
                UPDATE bookings 
                SET user_id = %s, test_id = %s, booking_date = %s, status = %s
                WHERE id = %s
            """, (user_id, test_id, booking_date, status, booking_id))
            conn.commit()

        return jsonify({'message': 'Booking updated successfully!'})
    except Exception as e:
        conn.rollback()
        logger.error("Error updating booking: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

# Delete Booking Route
@admin_bp.route('/bookings/delete', methods=['POST'])
@login_required
@role_required('admin')
def delete_booking():
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        booking_id = request.form.get('id')
        if not booking_id:
            return jsonify({'error': 'Booking ID is required'}), 400

        with conn.cursor() as cursor:
            cursor.execute("DELETE FROM bookings WHERE id = %s", (booking_id,))
            conn.commit()

        return jsonify({'message': 'Booking deleted successfully!'})
    except Exception as e:
        conn.rollback()
        logger.error("Error deleting booking: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

# Payments Route
@admin_bp.route('/payments')
@login_required
@role_required('admin')
def admin_payments():
    conn = get_db_connection()
    if not conn:
        return "Database connection failed", 500

    try:
        with conn.cursor(dictionary=True) as cursor:
            # Total Revenue
            cursor.execute("SELECT COALESCE(SUM(amount), 0) as total FROM payments WHERE payment_date >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)")
            total_revenue = cursor.fetchone()['total'] or 0
            
            # Pending Payments
            cursor.execute("SELECT COALESCE(SUM(p.amount), 0) as amount, COUNT(*) as count FROM payments p JOIN bookings b ON p.booking_id = b.id WHERE p.payment_status = 'pending'")
            pending = cursor.fetchone()
            pending_payments = pending['amount'] or 0
            pending_count = pending['count'] or 0
            
            # Failed Payments
            cursor.execute("SELECT COALESCE(SUM(p.amount), 0) as amount, COUNT(*) as count FROM payments p JOIN bookings b ON p.booking_id = b.id WHERE p.payment_status = 'failed'")
            failed = cursor.fetchone()
            failed_payments = failed['amount'] or 0
            failed_count = failed['count'] or 0
            
            # Refunds
            cursor.execute("SELECT COALESCE(SUM(amount), 0) as refunds FROM payments WHERE amount < 0 AND payment_date >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)")
            refunds = cursor.fetchone()['refunds'] or 0
            
            # Recent Payments
            cursor.execute("""
                SELECT 
                    p.transaction_id,
                    COALESCE(CONCAT(up.first_name, ' ', up.last_name), 'Unknown') as customer,
                    p.amount,
                    DATE_FORMAT(p.payment_date, '%Y-%m-%d') as date,
                    p.payment_status as status,
                    p.payment_method
                FROM payments p
                JOIN bookings b ON p.booking_id = b.id
                LEFT JOIN users u ON b.user_id = u.id
                LEFT JOIN user_profiles up ON u.id = up.user_id
                ORDER BY p.payment_date DESC 
                LIMIT 5
            """)
            recent_payments = cursor.fetchall()

            # Convert Decimal values to float for JSON serialization
            total_revenue = float(total_revenue)
            pending_payments = float(pending_payments)
            failed_payments = float(failed_payments)
            refunds = float(refunds)
            
            for payment in recent_payments:
                payment['amount'] = float(payment['amount'])

            return render_template('ADMIN/adminpayments.html',
                                total_revenue=total_revenue,
                                pending_payments=pending_payments,
                                pending_count=pending_count,
                                failed_payments=failed_payments,
                                failed_count=failed_count,
                                refunds=refunds,
                                recent_payments=recent_payments)

    except Exception as e:
        logger.error("Database Error: %s", str(e), exc_info=True)
        return f"Database error: {str(e)}", 500
    finally:
        conn.close()

# Payment Analytics API Endpoints
@admin_bp.route('/api/payments/analytics/trends')
@login_required
@role_required('admin')
def payment_trends_api():
    """API endpoint for payment trends over time"""
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        period = request.args.get('period', 'monthly')  # daily, weekly, monthly

        with conn.cursor(dictionary=True) as cursor:
            # Check if payments table has data
            cursor.execute("SELECT COUNT(*) as count FROM payments")
            payment_count = cursor.fetchone()['count']

            if payment_count == 0:
                # Return empty data if no payments exist
                return jsonify({
                    'success': True,
                    'data': [],
                    'period': period
                })

            if period == 'daily':
                # Last 30 days - simplified query
                cursor.execute("""
                    SELECT
                        DATE(payment_date) as date,
                        COUNT(*) as transaction_count,
                        COALESCE(SUM(amount), 0) as total_amount,
                        SUM(CASE WHEN payment_status = 'paid' THEN 1 ELSE 0 END) as successful_payments,
                        SUM(CASE WHEN payment_status = 'failed' THEN 1 ELSE 0 END) as failed_payments
                    FROM payments
                    WHERE payment_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                    GROUP BY DATE(payment_date)
                    ORDER BY DATE(payment_date)
                """)
            elif period == 'weekly':
                # Last 12 weeks - simplified approach
                cursor.execute("""
                    SELECT
                        YEARWEEK(payment_date) as week_year,
                        YEARWEEK(payment_date) as date,
                        COUNT(*) as transaction_count,
                        COALESCE(SUM(amount), 0) as total_amount,
                        SUM(CASE WHEN payment_status = 'paid' THEN 1 ELSE 0 END) as successful_payments,
                        SUM(CASE WHEN payment_status = 'failed' THEN 1 ELSE 0 END) as failed_payments
                    FROM payments
                    WHERE payment_date >= DATE_SUB(CURDATE(), INTERVAL 12 WEEK)
                    GROUP BY YEARWEEK(payment_date)
                    ORDER BY YEARWEEK(payment_date)
                """)
            else:  # monthly
                # Last 12 months - simplified approach
                cursor.execute("""
                    SELECT
                        DATE_FORMAT(payment_date, '%Y-%m') as date,
                        COUNT(*) as transaction_count,
                        COALESCE(SUM(amount), 0) as total_amount,
                        SUM(CASE WHEN payment_status = 'paid' THEN 1 ELSE 0 END) as successful_payments,
                        SUM(CASE WHEN payment_status = 'failed' THEN 1 ELSE 0 END) as failed_payments
                    FROM payments
                    WHERE payment_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                    GROUP BY DATE_FORMAT(payment_date, '%Y-%m')
                    ORDER BY DATE_FORMAT(payment_date, '%Y-%m')
                """)

            trends = cursor.fetchall()

            # Convert Decimal to float for JSON serialization
            for trend in trends:
                trend['total_amount'] = float(trend['total_amount'])
                # Remove week_year field if it exists (only for weekly)
                if 'week_year' in trend:
                    del trend['week_year']

            return jsonify({
                'success': True,
                'data': trends,
                'period': period
            })

    except Exception as e:
        logger.error("Payment trends API error: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@admin_bp.route('/api/payments/analytics/status-distribution')
@login_required
@role_required('admin')
def payment_status_distribution_api():
    """API endpoint for payment status distribution"""
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        with conn.cursor(dictionary=True) as cursor:
            cursor.execute("""
                SELECT
                    payment_status as status,
                    COUNT(*) as count,
                    COALESCE(SUM(amount), 0) as total_amount
                FROM payments
                WHERE payment_date >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
                GROUP BY payment_status
                ORDER BY count DESC
            """)

            distribution = cursor.fetchall()

            # Convert Decimal to float for JSON serialization
            for item in distribution:
                item['total_amount'] = float(item['total_amount'])

            return jsonify({
                'success': True,
                'data': distribution
            })

    except Exception as e:
        logger.error("Payment status distribution API error: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@admin_bp.route('/api/payments/analytics/methods')
@login_required
@role_required('admin')
def payment_methods_api():
    """API endpoint for payment methods comparison"""
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        with conn.cursor(dictionary=True) as cursor:
            cursor.execute("""
                SELECT
                    payment_method as method,
                    COUNT(*) as transaction_count,
                    COALESCE(SUM(amount), 0) as total_amount,
                    ROUND(AVG(amount), 2) as average_amount
                FROM payments
                WHERE payment_date >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
                AND payment_status = 'paid'
                GROUP BY payment_method
                ORDER BY total_amount DESC
            """)

            methods = cursor.fetchall()

            # Convert Decimal to float for JSON serialization
            for method in methods:
                method['total_amount'] = float(method['total_amount'])
                method['average_amount'] = float(method['average_amount'])

            return jsonify({
                'success': True,
                'data': methods
            })

    except Exception as e:
        logger.error("Payment methods API error: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@admin_bp.route('/api/payments/analytics/revenue')
@login_required
@role_required('admin')
def payment_revenue_api():
    """API endpoint for revenue analysis"""
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        with conn.cursor(dictionary=True) as cursor:
            # Check if payments table has data
            cursor.execute("SELECT COUNT(*) as count FROM payments")
            payment_count = cursor.fetchone()['count']

            if payment_count == 0:
                # Return empty data if no payments exist
                return jsonify({
                    'success': True,
                    'data': []
                })

            # Monthly revenue for the last 12 months - simplified query
            cursor.execute("""
                SELECT
                    DATE_FORMAT(payment_date, '%Y-%m') as month,
                    COALESCE(SUM(CASE WHEN payment_status = 'paid' THEN amount ELSE 0 END), 0) as revenue,
                    COALESCE(SUM(CASE WHEN payment_status = 'failed' THEN amount ELSE 0 END), 0) as failed_revenue,
                    SUM(CASE WHEN payment_status = 'paid' THEN 1 ELSE 0 END) as successful_transactions,
                    SUM(CASE WHEN payment_status = 'failed' THEN 1 ELSE 0 END) as failed_transactions
                FROM payments
                WHERE payment_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                GROUP BY DATE_FORMAT(payment_date, '%Y-%m')
                ORDER BY DATE_FORMAT(payment_date, '%Y-%m')
            """)

            revenue_data = cursor.fetchall()

            # Convert Decimal to float for JSON serialization
            for item in revenue_data:
                item['revenue'] = float(item['revenue'])
                item['failed_revenue'] = float(item['failed_revenue'])

            return jsonify({
                'success': True,
                'data': revenue_data
            })

    except Exception as e:
        logger.error("Payment revenue API error: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@admin_bp.route('/api/payments/analytics/success-rates')
@login_required
@role_required('admin')
def payment_success_rates_api():
    """API endpoint for payment success rates by method"""
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        with conn.cursor(dictionary=True) as cursor:
            cursor.execute("""
                SELECT
                    payment_method as method,
                    COUNT(*) as total_attempts,
                    COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as successful_payments,
                    COUNT(CASE WHEN payment_status = 'failed' THEN 1 END) as failed_payments,
                    ROUND(
                        (COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) * 100.0 / COUNT(*)),
                        2
                    ) as success_rate
                FROM payments
                WHERE payment_date >= DATE_SUB(CURDATE(), INTERVAL 6 MONTH)
                GROUP BY payment_method
                HAVING COUNT(*) >= 1  -- Include all payment methods with at least 1 attempt
                ORDER BY success_rate DESC, total_attempts DESC
            """)

            success_rates = cursor.fetchall()

            # Convert Decimal to float for JSON serialization
            for item in success_rates:
                item['success_rate'] = float(item['success_rate'])

            return jsonify({
                'success': True,
                'data': success_rates
            })

    except Exception as e:
        logger.error("Payment success rates API error: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

# Test Management Route
@admin_bp.route('/tests')
@login_required
@role_required('admin')
def admin_tests():
    conn = get_db_connection()
    if not conn:
        return "Database connection failed", 500

    try:
        with conn.cursor(dictionary=True) as cursor:
            # Get all tests with pagination support
            page = request.args.get('page', 1, type=int)
            per_page = 20
            offset = (page - 1) * per_page

            # Get total count
            cursor.execute("SELECT COUNT(*) as total FROM testdetails")
            total_tests = cursor.fetchone()['total']

            # Get tests with search and filter
            search = request.args.get('search', '')
            department = request.args.get('department', '')
            category = request.args.get('category', '')
            status = request.args.get('status', '')

            # Build query with filters
            where_conditions = []
            params = []

            if search:
                where_conditions.append("(TestName LIKE %s OR TestCode LIKE %s)")
                params.extend([f"%{search}%", f"%{search}%"])

            if department:
                where_conditions.append("DepartmentName = %s")
                params.append(department)

            if category:
                where_conditions.append("TestCategory = %s")
                params.append(category)

            if status:
                if status == 'active':
                    where_conditions.append("active = TRUE")
                elif status == 'inactive':
                    where_conditions.append("active = FALSE")

            where_clause = ""
            if where_conditions:
                where_clause = "WHERE " + " AND ".join(where_conditions)

            # Get filtered tests
            query = f"""
                SELECT
                    SrNo, TestName, TestCode, TestAmount, DepartmentName,
                    TestCategory, SampleType, TargetTAT, active,
                    created_at, updated_at
                FROM testdetails
                {where_clause}
                ORDER BY SrNo DESC
                LIMIT %s OFFSET %s
            """
            params.extend([per_page, offset])
            cursor.execute(query, params)
            tests = cursor.fetchall()

            # Get unique departments for filter
            cursor.execute("SELECT DISTINCT DepartmentName FROM testdetails WHERE DepartmentName IS NOT NULL ORDER BY DepartmentName")
            departments = [row['DepartmentName'] for row in cursor.fetchall()]

            # Get unique categories for filter
            cursor.execute("SELECT DISTINCT TestCategory FROM testdetails WHERE TestCategory IS NOT NULL ORDER BY TestCategory")
            categories = [row['TestCategory'] for row in cursor.fetchall()]

            # Calculate pagination
            total_pages = (total_tests + per_page - 1) // per_page

            return render_template('ADMIN/admintests.html',
                                tests=tests,
                                departments=departments,
                                categories=categories,
                                total_tests=total_tests,
                                current_page=page,
                                total_pages=total_pages,
                                search=search,
                                selected_department=department,
                                selected_category=category,
                                selected_status=status)

    except Exception as e:
        logger.error("Database Error in admin_tests: %s", str(e), exc_info=True)
        return f"Database error: {str(e)}", 500
    finally:
        conn.close()

# Get Test Details API
@admin_bp.route('/api/tests/<int:test_id>')
@login_required
@role_required('admin')
def get_test_details(test_id):
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        with conn.cursor(dictionary=True) as cursor:
            cursor.execute("""
                SELECT * FROM testdetails WHERE SrNo = %s
            """, (test_id,))
            test = cursor.fetchone()

            if not test:
                return jsonify({'error': 'Test not found'}), 404

            return jsonify(test)
    except Exception as e:
        logger.error("Error getting test details: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

# Create Test Route
@admin_bp.route('/tests/create', methods=['POST'])
@csrf_exempt
@login_required
@role_required('admin')
def create_test():
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['TestName', 'TestCode', 'TestAmount', 'DepartmentName', 'SampleType']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400

        with conn.cursor(dictionary=True) as cursor:
            # Check if test code already exists
            cursor.execute("SELECT SrNo FROM testdetails WHERE TestCode = %s", (data['TestCode'],))
            if cursor.fetchone():
                return jsonify({'error': 'Test code already exists'}), 400

            # Get next SrNo
            cursor.execute("SELECT COALESCE(MAX(SrNo), 0) + 1 as next_id FROM testdetails")
            next_id = cursor.fetchone()['next_id']

            # Insert new test
            cursor.execute("""
                INSERT INTO testdetails (
                    SrNo, TestName, TestCode, TestAmount, OutsourceAmount,
                    OutsourceCenter, SampleType, TestCategory, DepartmentName,
                    Accreditation, IntegrationCode, ShortText, CAPTest,
                    TargetTAT, VerificationStatus, TargetTATHHMM, active
                ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
            """, (
                next_id,
                data['TestName'],
                data['TestCode'],
                data['TestAmount'],
                data.get('OutsourceAmount', 0),
                data.get('OutsourceCenter', ''),
                data['SampleType'],
                data.get('TestCategory', ''),
                data['DepartmentName'],
                data.get('Accreditation', ''),
                data.get('IntegrationCode', ''),
                data.get('ShortText', ''),
                data.get('CAPTest', ''),
                data.get('TargetTAT', ''),
                data.get('VerificationStatus', ''),
                data.get('TargetTATHHMM', ''),
                data.get('active', True)
            ))
            conn.commit()

            # Log audit action
            log_audit_action(
                session.get('user_id'),
                session.get('username'),
                'CREATE_TEST',
                f"Created test: {data['TestName']} (Code: {data['TestCode']})",
                conn
            )

        return jsonify({'message': 'Test created successfully!', 'test_id': next_id})
    except Exception as e:
        conn.rollback()
        logger.error("Error creating test: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

# Update Test Route
@admin_bp.route('/tests/update/<int:test_id>', methods=['PUT'])
@csrf_exempt
@login_required
@role_required('admin')
def update_test(test_id):
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        data = request.get_json()

        # Validate required fields
        required_fields = ['TestName', 'TestCode', 'TestAmount', 'DepartmentName', 'SampleType']
        for field in required_fields:
            if not data.get(field):
                return jsonify({'error': f'{field} is required'}), 400

        with conn.cursor(dictionary=True) as cursor:
            # Check if test exists
            cursor.execute("SELECT TestName FROM testdetails WHERE SrNo = %s", (test_id,))
            existing_test = cursor.fetchone()
            if not existing_test:
                return jsonify({'error': 'Test not found'}), 404

            # Check if test code already exists for other tests
            cursor.execute("SELECT SrNo FROM testdetails WHERE TestCode = %s AND SrNo != %s", (data['TestCode'], test_id))
            if cursor.fetchone():
                return jsonify({'error': 'Test code already exists'}), 400

            # Update test
            cursor.execute("""
                UPDATE testdetails SET
                    TestName = %s, TestCode = %s, TestAmount = %s, OutsourceAmount = %s,
                    OutsourceCenter = %s, SampleType = %s, TestCategory = %s,
                    DepartmentName = %s, Accreditation = %s, IntegrationCode = %s,
                    ShortText = %s, CAPTest = %s, TargetTAT = %s,
                    VerificationStatus = %s, TargetTATHHMM = %s, active = %s,
                    updated_at = CURRENT_TIMESTAMP
                WHERE SrNo = %s
            """, (
                data['TestName'],
                data['TestCode'],
                data['TestAmount'],
                data.get('OutsourceAmount', 0),
                data.get('OutsourceCenter', ''),
                data['SampleType'],
                data.get('TestCategory', ''),
                data['DepartmentName'],
                data.get('Accreditation', ''),
                data.get('IntegrationCode', ''),
                data.get('ShortText', ''),
                data.get('CAPTest', ''),
                data.get('TargetTAT', ''),
                data.get('VerificationStatus', ''),
                data.get('TargetTATHHMM', ''),
                data.get('active', True),
                test_id
            ))
            conn.commit()

            # Log audit action
            log_audit_action(
                session.get('user_id'),
                session.get('username'),
                'UPDATE_TEST',
                f"Updated test: {data['TestName']} (ID: {test_id})",
                conn
            )

        return jsonify({'message': 'Test updated successfully!'})
    except Exception as e:
        conn.rollback()
        logger.error("Error updating test: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

# Delete Test Route
@admin_bp.route('/tests/delete/<int:test_id>', methods=['DELETE'])
@csrf_exempt
@login_required
@role_required('admin')
def delete_test(test_id):
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        with conn.cursor(dictionary=True) as cursor:
            # Check if test exists
            cursor.execute("SELECT TestName FROM testdetails WHERE SrNo = %s", (test_id,))
            test = cursor.fetchone()
            if not test:
                return jsonify({'error': 'Test not found'}), 404

            # Check if test is used in bookings
            cursor.execute("SELECT COUNT(*) as count FROM bookings WHERE test_id = %s", (test_id,))
            booking_count = cursor.fetchone()['count']

            if booking_count > 0:
                # Soft delete - just mark as inactive
                cursor.execute("UPDATE testdetails SET active = FALSE WHERE SrNo = %s", (test_id,))
                message = 'Test deactivated successfully (has existing bookings)'
                action = 'DEACTIVATE_TEST'
            else:
                # Hard delete - completely remove
                cursor.execute("DELETE FROM testdetails WHERE SrNo = %s", (test_id,))
                message = 'Test deleted successfully'
                action = 'DELETE_TEST'

            conn.commit()

            # Log audit action
            log_audit_action(
                session.get('user_id'),
                session.get('username'),
                action,
                f"Deleted/Deactivated test: {test['TestName']} (ID: {test_id})",
                conn
            )

        return jsonify({'message': message})
    except Exception as e:
        conn.rollback()
        logger.error("Error deleting test: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

# Export Payments to Excel
@admin_bp.route('/payments/export_excel', methods=['POST'])
@login_required
@role_required('admin')
def export_payments_to_excel():
    conn = get_db_connection()
    if not conn:
        return "Database connection failed", 500

    try:
        with conn.cursor(dictionary=True) as cursor:
            cursor.execute("""
                SELECT
                    p.transaction_id,
                    COALESCE(CONCAT(up.first_name, ' ', up.last_name), 'Unknown') as customer,
                    p.amount,
                    DATE_FORMAT(p.payment_date, '%Y-%m-%d') as date,
                    p.payment_status as status,
                    p.payment_method
                FROM payments p
                JOIN bookings b ON p.booking_id = b.id
                LEFT JOIN users u ON b.user_id = u.id
                LEFT JOIN user_profiles up ON u.id = up.user_id
                ORDER BY p.payment_date DESC
            """)
            payments = cursor.fetchall()

        wb = Workbook()
        ws = wb.active
        ws.title = "Payments Report"
        headers = ["Transaction ID", "Customer", "Amount", "Date", "Status", "Payment Method"]
        ws.append(headers)

        for payment in payments:
            ws.append([
                payment.get('transaction_id') or 'N/A',
                payment.get('customer') or 'Unknown',
                float(payment.get('amount', 0)),
                payment.get('date') or 'N/A',
                payment.get('status') or 'Unknown',
                payment.get('payment_method') or 'N/A'
            ])

        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        return send_file(
            output,
            download_name="payments_report.xlsx",
            as_attachment=True,
            mimetype="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        )

    except Exception as e:
        logger.error("Export Excel Error: %s", str(e), exc_info=True)
        return "Error exporting to Excel", 500
    finally:
        conn.close()

# Generate Payment Link
@admin_bp.route('/payments/generate_link', methods=['POST'])
@login_required
@role_required('admin')
def generate_payment_link():
    try:
        data = request.get_json()

        # Validate required fields
        email = data.get('email', '').strip()
        amount = data.get('amount')
        expiry_days = data.get('expiry', 7)
        share_via = data.get('share_via', [])

        if not email or not amount:
            return jsonify({'error': 'Email and amount are required'}), 400

        try:
            amount = float(amount)
            if amount <= 0:
                return jsonify({'error': 'Amount must be greater than 0'}), 400
        except (ValueError, TypeError):
            return jsonify({'error': 'Invalid amount format'}), 400

        # Generate unique payment link ID
        import uuid
        link_id = str(uuid.uuid4())

        # Calculate expiry date
        from datetime import datetime, timedelta
        expiry_date = datetime.now() + timedelta(days=int(expiry_days))

        # Create payment link data
        payment_link_data = {
            'link_id': link_id,
            'email': email,
            'amount': amount,
            'expiry_date': expiry_date,
            'created_by': session.get('user_id'),
            'status': 'active'
        }

        # Store in database (you may need to create a payment_links table)
        conn = get_db_connection()
        if conn:
            try:
                with conn.cursor(dictionary=True) as cursor:
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS payment_links (
                            id INT AUTO_INCREMENT PRIMARY KEY,
                            link_id VARCHAR(255) UNIQUE NOT NULL,
                            email VARCHAR(255) NOT NULL,
                            amount DECIMAL(10,2) NOT NULL,
                            expiry_date DATETIME NOT NULL,
                            created_by INT,
                            status ENUM('active', 'used', 'expired') DEFAULT 'active',
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            used_at TIMESTAMP NULL
                        )
                    """)

                    cursor.execute("""
                        INSERT INTO payment_links (link_id, email, amount, expiry_date, created_by, status)
                        VALUES (%s, %s, %s, %s, %s, %s)
                    """, (link_id, email, amount, expiry_date, payment_link_data['created_by'], 'active'))

                    conn.commit()
            except Exception as e:
                logger.error(f"Error storing payment link: {str(e)}", exc_info=True)
            finally:
                conn.close()

        # Generate the actual payment URL
        payment_url = f"{request.host_url}payment/link/{link_id}"

        # Send via email if requested
        if 'email' in share_via:
            try:
                # Use the new professional email service
                from email_service import get_email_service
                service = get_email_service()

                success = service.send_payment_link(
                    recipient=email,
                    customer_name="Valued Customer",
                    amount=float(amount),
                    payment_url=payment_url,
                    expiry_date=expiry_date,
                    request_id=link_id,
                    description=f"Payment request for ₹{amount:.2f}"
                )

                if not success:
                    # Fallback to simple email
                    raise Exception("Professional email service failed")

            except Exception as e:
                logger.error(f"Error sending professional payment link email: {str(e)}", exc_info=True)
                # Fallback to simple email
                try:
                    from flask_mail import Message
                    subject = 'CVBioLabs - Payment Link'
                    body = f"""
                    Dear Customer,

                    You have received a payment link from CVBioLabs.

                    Amount: ₹{amount:.2f}
                    Valid until: {expiry_date.strftime('%Y-%m-%d %H:%M:%S')}

                    Click here to make payment: {payment_url}

                    This link will expire on {expiry_date.strftime('%Y-%m-%d at %H:%M:%S')}.

                    Best regards,
                    CVBioLabs Team
                    """

                    msg = Message(
                        subject=subject,
                        recipients=[email],
                        body=body
                    )
                    mail.send(msg)

                except Exception as fallback_error:
                    logger.error(f"Fallback payment link email also failed: {str(fallback_error)}", exc_info=True)

        # Log audit action
        log_audit_action(
            session.get('user_id'),
            session.get('username'),
            'GENERATE_PAYMENT_LINK',
            f"Generated payment link for {email}, amount: ₹{amount}",
            conn if 'conn' in locals() else None
        )

        return jsonify({
            'success': True,
            'message': 'Payment link generated successfully',
            'email': email,
            'amount': amount,
            'link_id': link_id,
            'payment_url': payment_url,
            'expiry_date': expiry_date.isoformat()
        })

    except Exception as e:
        logger.error(f"Error generating payment link: {str(e)}", exc_info=True)
        return jsonify({'error': 'Failed to generate payment link'}), 500

# Print Payments Report (PDF)
@admin_bp.route('/payments/print_report', methods=['POST'])
@login_required
@role_required('admin')
def print_payments_report():
    conn = get_db_connection()
    if not conn:
        return "Database connection failed", 500

    try:
        with conn.cursor(dictionary=True) as cursor:
            # Get payment data with additional statistics
            cursor.execute("""
                SELECT
                    p.transaction_id,
                    COALESCE(CONCAT(up.first_name, ' ', up.last_name), 'Unknown') as customer,
                    p.amount,
                    DATE_FORMAT(p.payment_date, '%Y-%m-%d') as date,
                    TIME_FORMAT(p.payment_date, '%H:%i') as time,
                    p.payment_status as status,
                    p.payment_method
                FROM payments p
                JOIN bookings b ON p.booking_id = b.id
                LEFT JOIN users u ON b.user_id = u.id
                LEFT JOIN user_profiles up ON u.id = up.user_id
                ORDER BY p.payment_date DESC
            """)
            payments = cursor.fetchall()

            # Get summary statistics
            cursor.execute("""
                SELECT
                    COUNT(*) as total_transactions,
                    COALESCE(SUM(amount), 0) as total_amount,
                    COUNT(CASE WHEN payment_status = 'paid' THEN 1 END) as successful_payments,
                    COUNT(CASE WHEN payment_status = 'failed' THEN 1 END) as failed_payments,
                    COUNT(CASE WHEN payment_status = 'pending' THEN 1 END) as pending_payments,
                    MIN(payment_date) as earliest_payment,
                    MAX(payment_date) as latest_payment
                FROM payments p
                JOIN bookings b ON p.booking_id = b.id
            """)
            summary = cursor.fetchone()

        # Create professional PDF with CVBioLabs branding
        buffer = io.BytesIO()

        # Define CVBioLabs colors
        cvbio_orange = colors.Color(244/255, 124/255, 32/255)  # #f47c20
        cvbio_dark_blue = colors.Color(0/255, 47/255, 108/255)  # #002f6c
        cvbio_light_blue = colors.Color(230/255, 247/255, 255/255)  # #e6f7ff

        # Create document with custom margins (increased top margin for larger header)
        doc = BaseDocTemplate(
            buffer,
            pagesize=letter,
            rightMargin=0.75*inch,
            leftMargin=0.75*inch,
            topMargin=1.4*inch,
            bottomMargin=1*inch
        )

        # Create custom styles
        styles = getSampleStyleSheet()

        # Company header style
        company_style = ParagraphStyle(
            'CompanyHeader',
            parent=styles['Normal'],
            fontSize=24,
            textColor=cvbio_dark_blue,
            fontName='Helvetica-Bold',
            alignment=TA_CENTER,
            spaceAfter=6
        )

        # Report title style
        title_style = ParagraphStyle(
            'ReportTitle',
            parent=styles['Normal'],
            fontSize=18,
            textColor=cvbio_dark_blue,
            fontName='Helvetica-Bold',
            alignment=TA_CENTER,
            spaceAfter=20,
            spaceBefore=10
        )

        # Section header style
        section_style = ParagraphStyle(
            'SectionHeader',
            parent=styles['Normal'],
            fontSize=14,
            textColor=cvbio_dark_blue,
            fontName='Helvetica-Bold',
            alignment=TA_LEFT,
            spaceAfter=10,
            spaceBefore=15
        )

        # Normal text style
        normal_style = ParagraphStyle(
            'CustomNormal',
            parent=styles['Normal'],
            fontSize=10,
            fontName='Helvetica',
            alignment=TA_LEFT,
            spaceAfter=6
        )

        # Footer style
        footer_style = ParagraphStyle(
            'Footer',
            parent=styles['Normal'],
            fontSize=8,
            textColor=colors.grey,
            fontName='Helvetica',
            alignment=TA_CENTER
        )

        def draw_cvbio_logo(canvas, x, y, size=40):
            """Draw CVBioLabs logo using ReportLab graphics"""
            canvas.saveState()

            # Draw the circular elements (orange and blue)
            canvas.setStrokeColor(cvbio_orange)
            canvas.setFillColor(cvbio_orange)
            canvas.setLineWidth(3)

            # Orange arc/circle element
            canvas.circle(x - 10, y + 5, size/4, stroke=1, fill=0)

            # Blue arc/circle element
            canvas.setStrokeColor(cvbio_dark_blue)
            canvas.setFillColor(cvbio_dark_blue)
            canvas.circle(x + 10, y - 5, size/4, stroke=1, fill=0)

            # Draw the flask in the center
            canvas.setStrokeColor(colors.white)
            canvas.setFillColor(colors.white)
            canvas.setLineWidth(2)

            # Flask body (triangle/cone shape)
            flask_points = [
                (x - 8, y - 8),  # bottom left
                (x + 8, y - 8),  # bottom right
                (x + 4, y + 2),  # top right
                (x - 4, y + 2),  # top left
            ]

            # Draw flask outline using the correct ReportLab path methods
            p = canvas.beginPath()
            p.moveTo(flask_points[0][0], flask_points[0][1])
            for point in flask_points[1:]:
                p.lineTo(point[0], point[1])
            p.close()
            canvas.drawPath(p, stroke=1, fill=1)

            # Flask neck
            canvas.rect(x - 2, y + 2, 4, 8, stroke=1, fill=1)

            # Flask opening
            canvas.rect(x - 3, y + 10, 6, 2, stroke=1, fill=1)

            # Add "CV" text inside flask
            canvas.setFillColor(cvbio_dark_blue)
            canvas.setFont('Helvetica-Bold', 6)
            canvas.drawCentredString(x, y - 3, "CV")

            canvas.restoreState()

        def create_header_footer(canvas, doc):
            """Create header and footer for each page"""
            canvas.saveState()

            # Header with company branding
            canvas.setFillColor(cvbio_dark_blue)
            canvas.rect(0, letter[1] - 90, letter[0], 90, fill=1)

            # Company name and logo area
            canvas.setFillColor(colors.white)

            # Draw CVBioLabs logo
            logo_x = letter[0]/2 - 80
            logo_y = letter[1] - 45
            draw_cvbio_logo(canvas, logo_x, logo_y, 40)

            # Company name
            canvas.setFont('Helvetica-Bold', 24)
            canvas.drawString(letter[0]/2 - 40, letter[1] - 40, "CVBioLabs")

            # Tagline
            canvas.setFont('Helvetica', 10)
            canvas.setFillColor(cvbio_orange)
            canvas.drawString(letter[0]/2 - 40, letter[1] - 55, "for a healthy life")

            # Professional services text
            canvas.setFillColor(colors.white)
            canvas.setFont('Helvetica', 10)
            canvas.drawCentredString(letter[0]/2, letter[1] - 75, "Professional Laboratory Services")
            canvas.drawCentredString(letter[0]/2, letter[1] - 88, "Email: <EMAIL> | Phone: +91-XXXXXXXXXX")

            # Orange accent line
            canvas.setFillColor(cvbio_orange)
            canvas.rect(0, letter[1] - 95, letter[0], 5, fill=1)

            # Footer
            canvas.setFillColor(colors.grey)
            canvas.setFont('Helvetica', 8)
            canvas.drawCentredString(letter[0]/2, 30, f"Generated on {datetime.now().strftime('%B %d, %Y at %I:%M %p')}")
            canvas.drawCentredString(letter[0]/2, 20, "CVBioLabs - Confidential Document")

            # Page number
            canvas.drawRightString(letter[0] - 50, 30, f"Page {doc.page}")

            canvas.restoreState()

        # Create page template
        frame = Frame(
            doc.leftMargin, doc.bottomMargin,
            doc.width, doc.height,
            id='normal'
        )

        template = PageTemplate(id='main', frames=frame, onPage=create_header_footer)
        doc.addPageTemplates([template])

        # Build document content
        elements = []

        # Report title
        elements.append(Spacer(1, 20))
        elements.append(Paragraph("PAYMENT REPORT", title_style))
        elements.append(Spacer(1, 10))

        # Report generation info
        report_info = f"""
        <b>Report Generated:</b> {datetime.now().strftime('%B %d, %Y at %I:%M %p')}<br/>
        <b>Generated By:</b> {session.get('username', 'Admin')}<br/>
        <b>Report Period:</b> {summary.get('earliest_payment', 'N/A')} to {summary.get('latest_payment', 'N/A')}
        """
        elements.append(Paragraph(report_info, normal_style))
        elements.append(Spacer(1, 20))

        # Summary Statistics Section
        elements.append(Paragraph("SUMMARY STATISTICS", section_style))

        # Create summary statistics table
        summary_data = [
            ["Metric", "Value"],
            ["Total Transactions", f"{summary.get('total_transactions', 0):,}"],
            ["Total Amount", f"₹{float(summary.get('total_amount', 0)):,.2f}"],
            ["Successful Payments", f"{summary.get('successful_payments', 0):,}"],
            ["Failed Payments", f"{summary.get('failed_payments', 0):,}"],
            ["Pending Payments", f"{summary.get('pending_payments', 0):,}"],
        ]

        summary_table = Table(summary_data, colWidths=[2.5*inch, 2*inch])
        summary_table.setStyle(TableStyle([
            # Header styling
            ('BACKGROUND', (0, 0), (-1, 0), cvbio_dark_blue),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, 0), 12),
            ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
            ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
            ('TOPPADDING', (0, 0), (-1, 0), 12),

            # Data rows styling
            ('BACKGROUND', (0, 1), (-1, -1), cvbio_light_blue),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 1), (-1, -1), 10),
            ('ALIGN', (0, 1), (0, -1), 'LEFT'),
            ('ALIGN', (1, 1), (1, -1), 'RIGHT'),
            ('PADDING', (0, 1), (-1, -1), 8),

            # Borders
            ('LINEBELOW', (0, 0), (-1, 0), 2, cvbio_orange),
            ('GRID', (0, 0), (-1, -1), 1, colors.grey),
        ]))
        elements.append(summary_table)
        elements.append(Spacer(1, 30))

        # Payment Details Section
        elements.append(Paragraph("PAYMENT DETAILS", section_style))

        # Create payment data table
        if payments:
            # Table headers
            payment_data = [["Transaction ID", "Customer", "Amount", "Date", "Time", "Status", "Method"]]

            # Add payment rows
            for payment in payments:
                payment_data.append([
                    payment.get('transaction_id', 'N/A')[:20] + ('...' if len(payment.get('transaction_id', '')) > 20 else ''),
                    payment.get('customer', 'Unknown')[:15] + ('...' if len(payment.get('customer', '')) > 15 else ''),
                    f"₹{float(payment.get('amount', 0)):,.2f}",
                    payment.get('date', 'N/A'),
                    payment.get('time', 'N/A'),
                    (payment.get('status', 'Unknown')).capitalize(),
                    payment.get('payment_method', 'N/A')
                ])

            # Create table with appropriate column widths
            col_widths = [1.8*inch, 1.2*inch, 0.8*inch, 0.8*inch, 0.6*inch, 0.7*inch, 0.8*inch]
            payment_table = Table(payment_data, colWidths=col_widths, repeatRows=1)

            # Apply professional styling
            payment_table.setStyle(TableStyle([
                # Header styling
                ('BACKGROUND', (0, 0), (-1, 0), cvbio_dark_blue),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.white),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 9),
                ('ALIGN', (0, 0), (-1, 0), 'CENTER'),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 10),
                ('TOPPADDING', (0, 0), (-1, 0), 10),

                # Data rows styling - Clear background first
                ('BACKGROUND', (0, 1), (-1, -1), colors.white),  # Set all to white first
                ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
                ('FONTSIZE', (0, 1), (-1, -1), 8),
                ('ALIGN', (0, 1), (1, -1), 'LEFT'),  # Transaction ID and Customer left-aligned
                ('ALIGN', (2, 1), (2, -1), 'RIGHT'),  # Amount right-aligned
                ('ALIGN', (3, 1), (-1, -1), 'CENTER'),  # Date, Time, Status, Method center-aligned
                ('PADDING', (0, 1), (-1, -1), 6),
                ('VALIGN', (0, 1), (-1, -1), 'MIDDLE'),  # Vertical alignment

                # Borders - lighter and cleaner
                ('LINEBELOW', (0, 0), (-1, 0), 2, cvbio_orange),
                ('GRID', (0, 0), (-1, -1), 0.25, colors.lightgrey),

                # Remove any default text colors that might cause issues
                ('TEXTCOLOR', (0, 1), (-1, -1), colors.black),
            ]))

            # Apply alternating row colors manually to avoid conflicts
            for i in range(1, len(payment_data)):
                if i % 2 == 0:  # Even rows (0-indexed, so actually odd data rows)
                    payment_table.setStyle(TableStyle([
                        ('BACKGROUND', (0, i), (-1, i), cvbio_light_blue)
                    ]))

            # Apply conditional formatting for status column
            for i, payment in enumerate(payments, 1):
                status = payment.get('status', '').lower()
                if status == 'paid':
                    payment_table.setStyle(TableStyle([('TEXTCOLOR', (5, i), (5, i), colors.green)]))
                elif status == 'failed':
                    payment_table.setStyle(TableStyle([('TEXTCOLOR', (5, i), (5, i), colors.red)]))
                elif status == 'pending':
                    payment_table.setStyle(TableStyle([('TEXTCOLOR', (5, i), (5, i), cvbio_orange)]))

            elements.append(payment_table)
        else:
            elements.append(Paragraph("No payment data available for the selected period.", normal_style))

        elements.append(Spacer(1, 30))

        # Footer note
        footer_note = """
        <b>Note:</b> This report contains confidential financial information.
        Please handle with appropriate security measures and distribute only to authorized personnel.
        """
        elements.append(Paragraph(footer_note, normal_style))

        # Build the PDF
        try:
            doc.build(elements)
            buffer.seek(0)

            # Generate filename with timestamp
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"CVBioLabs_Payment_Report_{timestamp}.pdf"

            return send_file(
                buffer,
                download_name=filename,
                as_attachment=True,
                mimetype="application/pdf"
            )
        except Exception as pdf_error:
            logger.error("PDF Build Error: %s", str(pdf_error), exc_info=True)
            # Fallback to simple PDF if complex one fails
            buffer = io.BytesIO()
            doc = SimpleDocTemplate(buffer, pagesize=letter)
            elements = []
            styles = getSampleStyleSheet()

            elements.append(Paragraph("CVBioLabs - Payment Report", styles['Title']))
            elements.append(Paragraph(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M')}", styles['Normal']))
            elements.append(Paragraph("<br/><br/>", styles['Normal']))

            # Simple table
            data = [["Transaction ID", "Customer", "Amount", "Date", "Status", "Method"]]
            for payment in payments:
                data.append([
                    payment.get('transaction_id', 'N/A')[:20],
                    payment.get('customer', 'Unknown')[:15],
                    f"Rs.{float(payment.get('amount', 0)):.2f}",
                    payment.get('date', 'N/A'),
                    (payment.get('status', 'Unknown')).capitalize(),
                    payment.get('payment_method', 'N/A')
                ])

            table = Table(data)
            table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), colors.grey),
                ('TEXTCOLOR', (0, 0), (-1, 0), colors.whitesmoke),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, 0), 12),
                ('BOTTOMPADDING', (0, 0), (-1, 0), 12),
                ('BACKGROUND', (0, 1), (-1, -1), colors.beige),
                ('GRID', (0, 0), (-1, -1), 1, colors.black)
            ]))
            elements.append(table)

            doc.build(elements)
            buffer.seek(0)

            return send_file(
                buffer,
                download_name="payments_report_simple.pdf",
                as_attachment=True,
                mimetype="application/pdf"
            )

    except Exception as e:
        logger.error("Print Report Error: %s", str(e), exc_info=True)
        return "Error generating PDF report", 500
    finally:
        conn.close()

# Discounts Route
@admin_bp.route('/discounts')
@login_required
@role_required('admin')
def admin_discounts():
    conn = get_db_connection()
    if not conn:
        return "Database connection failed", 500

    try:
        with conn.cursor(dictionary=True) as cursor:
            # Active Discounts
            cursor.execute("""
                SELECT COALESCE(COUNT(*), 0) as active 
                FROM coupons 
                WHERE status = 'Active' 
                AND expiry_date >= CURDATE()
            """)
            active_discounts = cursor.fetchone()['active']
            
            # Total Uses
            cursor.execute("""
                SELECT COALESCE(COUNT(*), 0) as uses 
                FROM coupon_usage 
                WHERE used_at >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
            """)
            total_uses = cursor.fetchone()['uses']
            
            # Total Savings
            cursor.execute("""
                SELECT COALESCE(SUM(discount_amount), 0) as savings 
                FROM coupons 
                WHERE status = 'Used' 
                AND expiry_date >= DATE_SUB(CURDATE(), INTERVAL 1 MONTH)
            """)
            total_savings = cursor.fetchone()['savings']
            
            # Expiring Soon
            cursor.execute("""
                SELECT COALESCE(COUNT(*), 0) as expiring 
                FROM coupons 
                WHERE expiry_date BETWEEN CURDATE() 
                AND DATE_ADD(CURDATE(), INTERVAL 7 DAY)
            """)
            expiring_soon = cursor.fetchone()['expiring']
            
            # Discounts List
            cursor.execute("""
                SELECT 
                    id,
                    code,
                    COALESCE(discount_amount, 0) as discount_amount,
                    DATE(expiry_date) as valid_until,
                    COALESCE(status, 'Active') as status
                FROM coupons
                ORDER BY expiry_date DESC
            """)
            discounts = cursor.fetchall()

            # Convert Decimal to float for JSON serialization
            total_savings = float(total_savings) if total_savings else 0.0
            for discount in discounts:
                # Ensure all fields are present and valid
                discount['id'] = discount.get('id', '') or ''
                discount['code'] = discount.get('code', '') or ''
                discount['discount_amount'] = float(discount.get('discount_amount', 0) or 0)
                # valid_until as string YYYY-MM-DD or ''
                valid_until = discount.get('valid_until')
                if valid_until is None:
                    discount['valid_until'] = ''
                elif isinstance(valid_until, (datetime, date)):
                    discount['valid_until'] = valid_until.strftime('%Y-%m-%d')
                else:
                    discount['valid_until'] = str(valid_until)
                discount['status'] = discount.get('status', 'Active') or 'Active'

            return render_template('ADMIN/admindiscounts.html',
                                active_discounts=active_discounts,
                                total_uses=total_uses,
                                total_savings=total_savings,
                                expiring_soon=expiring_soon,
                                discounts=discounts)

    except Exception as e:
        logger.error("Database Error: %s", str(e), exc_info=True)
        return f"Database error: {str(e)}", 500
    finally:
        conn.close()

# Advertisement Management Routes
@admin_bp.route('/advertisements')
@login_required
@role_required('admin')
def admin_advertisements():
    conn = get_db_connection()
    if not conn:
        return "Database connection failed", 500

    try:
        cursor = conn.cursor(dictionary=True)

        # Get all advertisements with analytics
        cursor.execute("""
            SELECT
                a.*,
                au.name as created_by_name,
                COUNT(aa.id) as total_interactions,
                SUM(CASE WHEN aa.event_type = 'impression' THEN 1 ELSE 0 END) as impressions,
                SUM(CASE WHEN aa.event_type = 'click' THEN 1 ELSE 0 END) as clicks
            FROM advertisements a
            LEFT JOIN admin_users au ON a.created_by = au.id
            LEFT JOIN advertisement_analytics aa ON a.id = aa.ad_id
            GROUP BY a.id
            ORDER BY a.priority DESC, a.created_at DESC
        """)
        advertisements = cursor.fetchall()

        # Get summary statistics
        cursor.execute("""
            SELECT
                COUNT(*) as total_ads,
                SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active_ads,
                SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive_ads,
                SUM(CASE WHEN status = 'scheduled' THEN 1 ELSE 0 END) as scheduled_ads
            FROM advertisements
        """)
        stats = cursor.fetchone()

        return render_template('ADMIN/adminadvertisements.html',
                             advertisements=advertisements,
                             stats=stats)

    except Exception as e:
        logger.error("Database Error: %s", str(e), exc_info=True)
        return f"Database error: {str(e)}", 500
    finally:
        conn.close()

@admin_bp.route('/advertisements/create', methods=['POST'])
@login_required
@role_required('admin')
def create_advertisement():
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        data = request.get_json()
        cursor = conn.cursor()

        # Insert new advertisement
        cursor.execute("""
            INSERT INTO advertisements (
                title, description, image_url, link_url, position,
                ad_type, priority, status, start_date, end_date,
                target_audience, created_by
            ) VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
        """, (
            data['title'],
            data.get('description', ''),
            data.get('image_url', ''),
            data.get('link_url', ''),
            data['position'],
            data['ad_type'],
            data.get('priority', 1),
            data['status'],
            data.get('start_date'),
            data.get('end_date'),
            data.get('target_audience', 'all'),
            session.get('user_id')
        ))

        ad_id = cursor.lastrowid
        conn.commit()

        # Log audit action
        log_audit_action(
            session.get('user_id'),
            session.get('username'),
            'CREATE_ADVERTISEMENT',
            f"Created advertisement: {data['title']}",
            conn
        )

        return jsonify({'message': 'Advertisement created successfully!', 'ad_id': ad_id})

    except Exception as e:
        conn.rollback()
        logger.error("Error creating advertisement: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@admin_bp.route('/advertisements/update/<int:ad_id>', methods=['PUT'])
@login_required
@role_required('admin')
def update_advertisement(ad_id):
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        data = request.get_json()
        cursor = conn.cursor()

        # Update advertisement
        cursor.execute("""
            UPDATE advertisements SET
                title = %s, description = %s, image_url = %s, link_url = %s,
                position = %s, ad_type = %s, priority = %s, status = %s,
                start_date = %s, end_date = %s, target_audience = %s,
                updated_at = CURRENT_TIMESTAMP
            WHERE id = %s
        """, (
            data['title'],
            data.get('description', ''),
            data.get('image_url', ''),
            data.get('link_url', ''),
            data['position'],
            data['ad_type'],
            data.get('priority', 1),
            data['status'],
            data.get('start_date'),
            data.get('end_date'),
            data.get('target_audience', 'all'),
            ad_id
        ))

        conn.commit()

        # Log audit action
        log_audit_action(
            session.get('user_id'),
            session.get('username'),
            'UPDATE_ADVERTISEMENT',
            f"Updated advertisement ID: {ad_id}",
            conn
        )

        return jsonify({'message': 'Advertisement updated successfully!'})

    except Exception as e:
        conn.rollback()
        logger.error("Error updating advertisement: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@admin_bp.route('/advertisements/delete/<int:ad_id>', methods=['DELETE'])
@login_required
@role_required('admin')
def delete_advertisement(ad_id):
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        cursor = conn.cursor()

        # Get advertisement title for logging
        cursor.execute("SELECT title FROM advertisements WHERE id = %s", (ad_id,))
        ad_title = cursor.fetchone()

        # Delete advertisement (analytics will be deleted by CASCADE)
        cursor.execute("DELETE FROM advertisements WHERE id = %s", (ad_id,))
        conn.commit()

        # Log audit action
        log_audit_action(
            session.get('user_id'),
            session.get('username'),
            'DELETE_ADVERTISEMENT',
            f"Deleted advertisement: {ad_title[0] if ad_title else f'ID {ad_id}'}",
            conn
        )

        return jsonify({'message': 'Advertisement deleted successfully!'})

    except Exception as e:
        conn.rollback()
        logger.error("Error deleting advertisement: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

# Advertisement Image Upload Route
@admin_bp.route('/advertisements/upload-image', methods=['POST'])
@login_required
@role_required('admin')
def upload_advertisement_image():
    """Handle advertisement image upload with optional cropping"""
    try:
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Check file type
        allowed_extensions = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
        if not ('.' in file.filename and file.filename.rsplit('.', 1)[1].lower() in allowed_extensions):
            return jsonify({'error': 'Invalid file type. Allowed: PNG, JPG, JPEG, GIF, WEBP'}), 400

        # Create uploads directory if it doesn't exist
        from flask import current_app
        upload_dir = os.path.join(current_app.root_path, 'static', 'uploads', 'advertisements')
        os.makedirs(upload_dir, exist_ok=True)

        # Generate unique filename
        import uuid
        from datetime import datetime
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        unique_id = str(uuid.uuid4())[:8]
        file_extension = file.filename.rsplit('.', 1)[1].lower()
        filename = f"ad_{timestamp}_{unique_id}.{file_extension}"

        # Save original file
        file_path = os.path.join(upload_dir, filename)
        file.save(file_path)

        # Get crop parameters if provided
        crop_data = request.form.get('crop_data')
        if crop_data:
            try:
                import json
                from PIL import Image

                crop_info = json.loads(crop_data)

                # Open and crop image
                with Image.open(file_path) as img:
                    # Convert to RGB if necessary
                    if img.mode in ('RGBA', 'LA', 'P'):
                        img = img.convert('RGB')

                    # Apply crop
                    x = int(crop_info.get('x', 0))
                    y = int(crop_info.get('y', 0))
                    width = int(crop_info.get('width', img.width))
                    height = int(crop_info.get('height', img.height))

                    # Ensure crop dimensions are within image bounds
                    x = max(0, min(x, img.width))
                    y = max(0, min(y, img.height))
                    width = min(width, img.width - x)
                    height = min(height, img.height - y)

                    cropped_img = img.crop((x, y, x + width, y + height))

                    # Resize if specified
                    target_width = crop_info.get('target_width')
                    target_height = crop_info.get('target_height')
                    if target_width and target_height:
                        cropped_img = cropped_img.resize((int(target_width), int(target_height)), Image.Resampling.LANCZOS)

                    # Save cropped image
                    cropped_img.save(file_path, quality=90, optimize=True)

            except Exception as e:
                logger.error(f"Error processing image crop: {str(e)}")
                # Continue with original image if cropping fails

        # Return the URL for the uploaded image
        image_url = f"/static/uploads/advertisements/{filename}"

        # Log the upload
        log_audit_action(
            session.get('user_id'),
            session.get('username'),
            'UPLOAD_AD_IMAGE',
            f"Uploaded advertisement image: {filename}",
            None
        )

        return jsonify({
            'success': True,
            'image_url': image_url,
            'filename': filename,
            'message': 'Image uploaded successfully'
        })

    except Exception as e:
        logger.error(f"Error uploading advertisement image: {str(e)}", exc_info=True)
        return jsonify({'error': 'Failed to upload image'}), 500

# Users Route
@admin_bp.route('/users')
@login_required
@role_required('admin')
def admin_users():
    conn = get_db_connection()
    if not conn:
        return "Database connection failed", 500

    try:
        with conn.cursor(dictionary=True) as cursor:  # Changed to dictionary cursor
            # Get admin user counts by role
            cursor.execute("""
                SELECT 
                    COUNT(CASE WHEN role = 'Doctor' THEN 1 END) as doctors,
                    COUNT(CASE WHEN role = 'Receptionist' THEN 1 END) as receptionists,
                    COUNT(CASE WHEN role = 'Admin' THEN 1 END) as admins
                FROM admin_users
                WHERE status = 'active'
            """)
            admin_counts = cursor.fetchone()
            
            # Get patient count
            cursor.execute("""
                SELECT COUNT(*) as patients
                FROM users
                WHERE status = 1
            """)
            patient_count = cursor.fetchone()['patients'] or 0
            
            # Get all admin users
            cursor.execute("""
                SELECT 
                    id,
                    professional_id,
                    name,
                    email,
                    phone,
                    role,
                    status,
                    created_at,
                    'admin' as user_type
                FROM admin_users
                ORDER BY id DESC
            """)
            admin_users = cursor.fetchall()

            # Get all regular users (patients)
            cursor.execute("""
                SELECT 
                    u.id,
                    u.username as professional_id,
                    CONCAT(up.first_name, ' ', up.last_name) as name,
                    u.email,
                    up.phone,
                    'Patient' as role,
                    CASE WHEN u.status = 1 THEN 'active' ELSE 'inactive' END as status,
                    u.created_at,
                    'patient' as user_type
                FROM users u
                LEFT JOIN user_profiles up ON u.id = up.user_id
                ORDER BY u.id DESC
            """)
            patients = cursor.fetchall()

            # Get all pickup agents
            cursor.execute("""
                SELECT 
                    id,
                    professional_id,
                    name,
                    email,
                    phone,
                    'Pickup Agent' as role,
                    CASE 
                        WHEN status = 'Available' THEN 'active'
                        WHEN status = 'Inactive' THEN 'inactive'
                        ELSE status
                    END as status,
                    created_at,
                    'agent' as user_type
                FROM pickup_agents
                ORDER BY id DESC
            """)
            agents = cursor.fetchall()

            # Convert all results to lists and combine them
            all_users = list(admin_users) + list(patients) + list(agents)

            # Convert datetime objects to strings for JSON serialization
            for user in all_users:
                if 'created_at' in user and user['created_at']:
                    user['created_at'] = user['created_at'].strftime('%Y-%m-%d %H:%M:%S')

            return render_template('ADMIN/adminusers.html',
                                users=all_users,
                                doctors=admin_counts['doctors'] or 0,
                                receptionists=admin_counts['receptionists'] or 0,
                                patients=patient_count,
                                agents=len(agents))

    except Exception as e:
        logger.error("Database Error: %s", str(e), exc_info=True)
        return f"Database error: {str(e)}", 500
    finally:
        conn.close()

def generate_professional_id(role, conn):
    """Generate a professional ID based on role and current count"""
    try:
        with conn.cursor(dictionary=True) as cursor:
            # Get current year
            current_year = datetime.now().year % 100  # Get last 2 digits of year
            
            # Get the count of existing IDs for this role in current year
            if role == 'Doctor':
                prefix = 'DOC'
                cursor.execute("""
                    SELECT COUNT(*) as count 
                    FROM admin_users 
                    WHERE role = 'Doctor' 
                    AND professional_id LIKE %s
                """, (f'{prefix}{current_year}%',))
            elif role == 'Receptionist':
                prefix = 'REC'
                cursor.execute("""
                    SELECT COUNT(*) as count 
                    FROM admin_users 
                    WHERE role = 'Receptionist' 
                    AND professional_id LIKE %s
                """, (f'{prefix}{current_year}%',))
            elif role == 'Pickup Agent':
                prefix = 'AGT'
                cursor.execute("""
                    SELECT COUNT(*) as count 
                    FROM pickup_agents 
                    WHERE professional_id LIKE %s
                """, (f'{prefix}{current_year}%',))
            
            result = cursor.fetchone()
            count = result['count'] + 1 if result else 1
            
            # Generate ID in format: PREFIX + YY + 4-digit sequence
            professional_id = f"{prefix}{current_year}{count:04d}"
            return professional_id
            
    except Exception as e:
        print(f"Error generating professional ID: {e}")
        return None

def send_welcome_email(user_data, professional_id, user_type, password=None):
    try:
        subject = f'Welcome to CVBioLabs - {user_type} Account Created'
        
        # Create email body based on user type
        if user_type == 'agent':
            body = f"""
            Dear {user_data['name']},

            Welcome to CVBioLabs! Your pickup agent account has been created successfully.

            Your account details:
            Professional ID: {professional_id}
            Email: {user_data['email']}
            Phone: {user_data['phone']}
            Password: {password if password else 'Your existing password'}

            You can now login to your account using your email and password.

            Best regards,
            CVBioLabs Team
            """
        elif user_type == 'Doctor':
            body = f"""
            Dear Dr. {user_data['name']},

            Welcome to CVBioLabs! Your doctor account has been created successfully.

            Your account details:
            Professional ID: {professional_id}
            Email: {user_data['email']}
            Phone: {user_data['phone']}
            Role: Doctor
            Password: {password if password else 'Your existing password'}

            You can now login to your account using your email and password.

            Best regards,
            CVBioLabs Team
            """
        else:
            role = user_data.get('role', 'Receptionist')
            body = f"""
            Dear {user_data['name']},

            Welcome to CVBioLabs! Your {role} account has been created successfully.

            Your account details:
            Professional ID: {professional_id}
            Email: {user_data['email']}
            Phone: {user_data['phone']}
            Role: {role}
            Password: {password if password else 'Your existing password'}

            You can now login to your account using your email and password.

            Best regards,
            CVBioLabs Team
            """

        msg = Message(
            subject=subject,
            recipients=[user_data['email']],
            body=body
        )
        mail.send(msg)
        return True
    except Exception as e:
        logger.error("Error sending welcome email: %s", str(e), exc_info=True)
        return False

@admin_bp.route('/users/save', methods=['POST'])
@login_required
@role_required('admin')
def save_user():
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        data = request.get_json()
        user_id = data.get('id')
        user_type = data.get('userType', 'admin')  # Default to admin if not specified
        user_data = {
            'name': data.get('name'),
            'email': data.get('email'),
            'phone': data.get('phone'),
            'status': data.get('status', 'active'),
            'password': data.get('password')  # We'll handle password hashing
        }

        # Store original password for email
        original_password = user_data['password']

        # Add role only for admin users
        if user_type != 'agent':
            user_data['role'] = data.get('role')
            if not user_data['role']:
                return jsonify({'error': 'Role is required for admin users'}), 400

        # Validate required fields
        required_fields = ['name', 'email', 'phone']
        if not all(user_data[field] for field in required_fields):
            return jsonify({'error': 'All fields are required'}), 400

        # Validate role for admin users
        if user_type != 'agent':
            valid_roles = ['Admin', 'Doctor', 'Receptionist']
            if user_data['role'] not in valid_roles:
                return jsonify({'error': f'Invalid role. Must be one of: {", ".join(valid_roles)}'}), 400

        # Check for existing credentials across all tables
        with conn.cursor(dictionary=True) as cursor:
            # Check email in all tables
            cursor.execute("""
                SELECT 'users' as table_name, id FROM users WHERE email = %s AND id != %s
                UNION ALL
                SELECT 'admin_users' as table_name, id FROM admin_users WHERE email = %s AND id != %s
                UNION ALL
                SELECT 'doctors' as table_name, id FROM doctors WHERE email = %s AND id != %s
                UNION ALL
                SELECT 'pickup_agents' as table_name, id FROM pickup_agents WHERE email = %s AND id != %s
            """, (
                user_data['email'], user_id or 0,
                user_data['email'], user_id or 0,
                user_data['email'], user_id or 0,
                user_data['email'], user_id or 0
            ))
            existing_email = cursor.fetchone()
            
            if existing_email:
                return jsonify({
                    'error': f'Email already registered as {existing_email["table_name"].replace("_", " ").title()}'
                }), 400

            # Check phone in all tables
            cursor.execute("""
                SELECT 'user_profiles' as table_name, user_id FROM user_profiles WHERE phone = %s AND user_id != %s
                UNION ALL
                SELECT 'admin_users' as table_name, id FROM admin_users WHERE phone = %s AND id != %s
                UNION ALL
                SELECT 'doctors' as table_name, id FROM doctors WHERE phone = %s AND id != %s
                UNION ALL
                SELECT 'pickup_agents' as table_name, id FROM pickup_agents WHERE phone = %s AND id != %s
            """, (
                user_data['phone'], user_id or 0,
                user_data['phone'], user_id or 0,
                user_data['phone'], user_id or 0,
                user_data['phone'], user_id or 0
            ))
            existing_phone = cursor.fetchone()
            
            if existing_phone:
                return jsonify({
                    'error': f'Phone number already registered as {existing_phone["table_name"].replace("_", " ").title()}'
                }), 400

            # Hash the password if provided
            if user_data.get('password') and user_data['password'].strip():
                user_data['password'] = bcrypt.hashpw(user_data['password'].encode(), bcrypt.gensalt())
            else:
                user_data['password'] = None

            if user_type == 'agent':
                # Map status for pickup agents
                agent_status = 'Available' if user_data['status'] == 'active' else 'Inactive'
                
                if user_id:
                    # Update existing pickup agent
                    cursor.execute("""
                        UPDATE pickup_agents 
                        SET name = %s, email = %s, phone = %s, 
                            status = %s, vehicle_number = %s, service_area = %s
                        WHERE id = %s
                    """, (
                        user_data['name'],
                        user_data['email'],
                        user_data['phone'],
                        agent_status,
                        data.get('vehicleNumber'),
                        data.get('serviceArea'),
                        user_id
                    ))
                    
                    # Send welcome email for existing agent with password if changed
                    send_welcome_email(user_data, professional_id, 'Pickup Agent', 
                                    password=original_password if original_password else None)
                    
                    message = 'Pickup agent updated successfully'
                else:
                    # Create new pickup agent
                    if not user_data.get('password') or not user_data['password']:
                        return jsonify({'error': 'Password is required for new pickup agents'}), 400
                    
                    # Generate professional ID for new agent
                    professional_id = generate_professional_id('Pickup Agent', conn)
                    if not professional_id:
                        return jsonify({'error': 'Failed to generate professional ID'}), 500
                        
                    # Debug logging
                    logger.info(f"Creating pickup agent with data: name={user_data['name']}, email={user_data['email']}, phone={user_data['phone']}, status={agent_status}, professional_id={professional_id}")

                    cursor.execute("""
                        INSERT INTO pickup_agents (
                            name, email, phone, status, password_hash,
                            professional_id, vehicle_number, service_area
                        )
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        user_data['name'],
                        user_data['email'],
                        user_data['phone'],
                        agent_status,
                        user_data['password'],
                        professional_id,
                        data.get('vehicleNumber'),
                        data.get('serviceArea')
                    ))
                    
                    # Send welcome email for new agent with password
                    send_welcome_email(user_data, professional_id, 'Pickup Agent', password=original_password)
                    
                    new_user_id = cursor.lastrowid
                    message = f'Pickup agent created successfully with ID: {professional_id}'
            else:
                if user_id:
                    # Update existing admin user
                    if user_data['password']:
                        cursor.execute("""
                            UPDATE admin_users 
                            SET name = %s, email = %s, phone = %s, 
                                role = %s, status = %s, password_hash = %s
                            WHERE id = %s
                        """, (
                            user_data['name'],
                            user_data['email'],
                            user_data['phone'],
                            user_data['role'],
                            user_data['status'],
                            user_data['password'],
                            user_id
                        ))
                    else:
                        cursor.execute("""
                            UPDATE admin_users 
                            SET name = %s, email = %s, phone = %s, 
                                role = %s, status = %s
                            WHERE id = %s
                        """, (
                            user_data['name'],
                            user_data['email'],
                            user_data['phone'],
                            user_data['role'],
                            user_data['status'],
                            user_id
                        ))
                    
                    # If user is a doctor, update doctors table
                    if user_data['role'] == 'Doctor':
                        cursor.execute("""
                            UPDATE doctors 
                            SET name = %s, email = %s, phone = %s, 
                                specialization = %s, licence_number = %s, status = %s,
                                password_hash = %s
                            WHERE id = %s
                        """, (
                            user_data['name'],
                            user_data['email'],
                            user_data['phone'],
                            data.get('specialization'),
                            data.get('licenseNumber'),
                            user_data['status'],
                            user_data['password'] if user_data['password'] else None,
                            user_id
                        ))
                    
                    # Send welcome email for existing admin user with password if changed
                    send_welcome_email(user_data, professional_id, user_data['role'], 
                                    password=original_password if original_password else None)
                    
                    message = 'Admin user updated successfully'
                else:
                    # Create new admin user
                    if not user_data['password']:
                        return jsonify({'error': 'Password is required for new users'}), 400
                    
                    # Generate professional ID for new users
                    professional_id = generate_professional_id(user_data['role'], conn)
                    if not professional_id:
                        return jsonify({'error': 'Failed to generate professional ID'}), 500
                        
                    cursor.execute("""
                        INSERT INTO admin_users (name, email, phone, role, status, password_hash, professional_id)
                        VALUES (%s, %s, %s, %s, %s, %s, %s)
                    """, (
                        user_data['name'],
                        user_data['email'],
                        user_data['phone'],
                        user_data['role'],
                        user_data['status'],
                        user_data['password'],
                        professional_id
                    ))
                    new_user_id = cursor.lastrowid
                    
                    # If user is a doctor, insert into doctors table
                    if user_data['role'] == 'Doctor':
                        cursor.execute("""
                            INSERT INTO doctors (
                                name, email, phone, specialization, 
                                licence_number, status, professional_id,
                                password_hash
                            )
                            VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                        """, (
                            user_data['name'],
                            user_data['email'],
                            user_data['phone'],
                            data.get('specialization'),
                            data.get('licenseNumber'),
                            user_data['status'],
                            professional_id,
                            user_data['password']
                        ))
                    
                    # Send welcome email for new admin user with password
                    send_welcome_email(user_data, professional_id, user_data['role'], password=original_password)
                    
                    message = f'Admin user created successfully with ID: {professional_id}'

            conn.commit()
            return jsonify({
                'message': message, 
                'success': True,
                'user': {
                    'id': new_user_id if not user_id else user_id,
                    'professional_id': professional_id,
                    'name': user_data['name'],
                    'email': user_data['email'],
                    'phone': user_data['phone'],
                    'role': user_data.get('role', 'Pickup Agent'),
                    'status': user_data['status']
                }
            })

    except Exception as e:
        conn.rollback()
        logger.error("Error saving user: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

# Password Reset Route
@admin_bp.route('/reset-password')
@login_required
@role_required('admin')
def admin_reset_password():
    return render_template('ADMIN/adminreset_password.html')

@admin_bp.route('/users/delete/<int:user_id>', methods=['DELETE'])
@login_required
@role_required('admin')
def delete_user(user_id):
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        with conn.cursor(dictionary=True) as cursor:  # <-- Use dictionary cursor
            # First check if it's a pickup agent
            cursor.execute("SELECT id FROM pickup_agents WHERE id = %s", (user_id,))
            is_agent = cursor.fetchone() is not None

            if is_agent:
                # Delete from pickup_agents table
                cursor.execute("DELETE FROM pickup_agents WHERE id = %s", (user_id,))
            else:
                # Check if it's an admin user
                cursor.execute("SELECT id, role FROM admin_users WHERE id = %s", (user_id,))
                admin_user = cursor.fetchone()

                if admin_user:
                    # If it's a doctor, delete from both tables
                    if admin_user['role'] == 'Doctor':
                        cursor.execute("DELETE FROM doctors WHERE id = %s", (user_id,))
                    cursor.execute("DELETE FROM admin_users WHERE id = %s", (user_id,))
                else:
                    # For patients, delete from both tables
                    cursor.execute("DELETE FROM user_profiles WHERE user_id = %s", (user_id,))
                    cursor.execute("DELETE FROM users WHERE id = %s", (user_id,))

            conn.commit()
            
# After conn.commit() and before returning the response in delete_user:

            log_audit_action(
                session.get('user_id'),
                session.get('username'),
                "delete_user",
                f"Deleted user with ID: {user_id}",
                conn
            )
            return jsonify({'message': 'User deleted successfully', 'success': True})
    except Exception as e:
        conn.rollback()
        logger.error("Error deleting user: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@admin_bp.route('/users/status/<int:user_id>', methods=['PUT'])
@login_required
@role_required('admin')
def update_user_status(user_id):
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        new_status = request.json.get('status')
        if not new_status:
            return jsonify({'error': 'Status is required'}), 400

        with conn.cursor() as cursor:
            # First check if it's a pickup agent
            cursor.execute("SELECT id FROM pickup_agents WHERE id = %s", (user_id,))
            is_agent = cursor.fetchone() is not None

            if is_agent:
                # Map status for pickup agents
                agent_status = 'Available' if new_status == 'active' else 'Inactive'
                cursor.execute("""
                    UPDATE pickup_agents 
                    SET status = %s 
                    WHERE id = %s
                """, (agent_status, user_id))
            else:
                # Check if it's an admin user
                cursor.execute("SELECT id FROM admin_users WHERE id = %s", (user_id,))
                is_admin = cursor.fetchone() is not None

                if is_admin:
                    cursor.execute("""
                        UPDATE admin_users 
                        SET status = %s 
                        WHERE id = %s
                    """, (new_status, user_id))
                else:
                    # For patients, update the status in users table
                    cursor.execute("""
                        UPDATE users 
                        SET status = %s 
                        WHERE id = %s
                    """, (1 if new_status == 'active' else 0, user_id))

            conn.commit()

            # After conn.commit() and before returning the response in update_user_status:

            log_audit_action(
                session.get('user_id'),
                session.get('username'),
                "update_user_status",
                f"Changed status for user ID: {user_id} to {new_status}",
                conn
            )
            return jsonify({
                'message': 'Status updated successfully', 
                'success': True,
                'new_status': agent_status if is_agent else new_status
            })
    except Exception as e:
        conn.rollback()
        logger.error("Error updating status: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@admin_bp.route('/users/data')
@login_required
@role_required('admin')
def get_users_data():
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        # Get filter parameters from request
        user_type = request.args.get('userType', 'all')
        search_term = request.args.get('search', '')

        with conn.cursor(dictionary=True) as cursor:  # Changed to dictionary cursor
            # Initialize queries and parameters
            queries = []

            # Add admin users query if needed
            if user_type in ['all', 'Doctor', 'Receptionist']:
                admin_query = """
                    SELECT 
                        id,
                        professional_id,
                        name,
                        email,
                        phone,
                        role,
                        status,
                        created_at,
                        'admin' as user_type
                    FROM admin_users
                    WHERE 1=1
                """
                admin_params = []

                if user_type != 'all':
                    admin_query += " AND role = %s"
                    admin_params.append(user_type)

                if search_term:
                    search_condition = " AND (name LIKE %s OR email LIKE %s OR phone LIKE %s OR professional_id LIKE %s)"
                    search_param = f"%{search_term}%"
                    admin_query += search_condition
                    admin_params.extend([search_param] * 4)

                queries.append((admin_query, admin_params))

            # Add patients query if needed
            if user_type in ['all', 'Patient']:
                patient_query = """
                    SELECT 
                        u.id,
                        u.username as professional_id,
                        CONCAT(up.first_name, ' ', up.last_name) as name,
                        u.email,
                        up.phone,
                        'Patient' as role,
                        CASE WHEN u.status = 1 THEN 'active' ELSE 'inactive' END as status,
                        u.created_at,
                        'patient' as user_type
                    FROM users u
                    LEFT JOIN user_profiles up ON u.id = up.user_id
                    WHERE 1=1
                """
                patient_params = []

                if search_term:
                    search_condition = " AND (u.username LIKE %s OR u.email LIKE %s OR up.phone LIKE %s OR CONCAT(up.first_name, ' ', up.last_name) LIKE %s)"
                    search_param = f"%{search_term}%"
                    patient_query += search_condition
                    patient_params.extend([search_param] * 4)

                queries.append((patient_query, patient_params))

            # Add pickup agents query if needed
            if user_type in ['all', 'Pickup Agent']:
                agent_query = """
                    SELECT 
                        id,
                        professional_id,
                        name,
                        email,
                        phone,
                        'Pickup Agent' as role,
                        CASE 
                            WHEN status = 'Available' THEN 'active'
                            WHEN status = 'Inactive' THEN 'inactive'
                            ELSE status
                        END as status,
                        created_at,
                        'agent' as user_type
                    FROM pickup_agents
                    WHERE 1=1
                """
                agent_params = []

                if search_term:
                    search_condition = " AND (name LIKE %s OR email LIKE %s OR phone LIKE %s OR professional_id LIKE %s)"
                    search_param = f"%{search_term}%"
                    agent_query += search_condition
                    agent_params.extend([search_param] * 4)

                queries.append((agent_query, agent_params))

            # Execute all queries and combine results
            all_users = []
            for query, query_params in queries:
                cursor.execute(query, query_params)
                users = cursor.fetchall()
                
                # Convert datetime objects to strings for JSON serialization
                for user in users:
                    if 'created_at' in user and user['created_at']:
                        user['created_at'] = user['created_at'].strftime('%Y-%m-%d %H:%M:%S')
                
                all_users.extend(users)

            # Get counts for active users
            cursor.execute("""
                SELECT 
                    COUNT(CASE WHEN role = 'Doctor' AND status = 'active' THEN 1 END) as doctors,
                    COUNT(CASE WHEN role = 'Receptionist' AND status = 'active' THEN 1 END) as receptionists
                FROM admin_users
            """)
            admin_counts = cursor.fetchone()

            cursor.execute("SELECT COUNT(*) as patients FROM users WHERE status = 1")
            patient_count = cursor.fetchone()['patients'] or 0

            cursor.execute("SELECT COUNT(*) as agents FROM pickup_agents WHERE status = 'Available'")
            agent_count = cursor.fetchone()['agents'] or 0

            return jsonify({
                'users': all_users,
                'counts': {
                    'doctors': admin_counts['doctors'] or 0,
                    'receptionists': admin_counts['receptionists'] or 0,
                    'patients': patient_count,
                    'agents': agent_count
                }
            })

    except Exception as e:
        logger.error("Database Error: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@admin_bp.route('/api/placeholder/<int:width>/<int:height>')
def placeholder_image(width, height):
    # Create a new image with the specified dimensions
    img = Image.new('RGB', (width, height), color='#002f6c')
    draw = ImageDraw.Draw(img)
    
    # Add text to the image
    try:
        # Try to use a system font
        font = ImageFont.truetype("arial.ttf", 20)
    except:
        # Fallback to default font
        font = ImageFont.load_default()
    
    text = "CVBIO LABS"
    text_width = draw.textlength(text, font=font)
    text_height = 20
    
    # Calculate position to center the text
    x = (width - text_width) // 2
    y = (height - text_height) // 2
    
    # Draw the text
    draw.text((x, y), text, fill='white', font=font)
    
    # Save the image to a bytes buffer
    img_byte_arr = io.BytesIO()
    img.save(img_byte_arr, format='PNG')
    img_byte_arr.seek(0)
    
    return send_file(img_byte_arr, mimetype='image/png')

@admin_bp.route('/users/generate-password', methods=['GET'])
def generate_password():
    # Generate a secure random password
    alphabet = string.ascii_letters + string.digits + string.punctuation
    password = ''.join(secrets.choice(alphabet) for _ in range(12))
    return jsonify({'password': password})




def send_password_reset_email(user_data, professional_id, user_type, password):
    try:
        # Use the new professional email service
        from email_service import get_email_service
        service = get_email_service()

        success = service.send_admin_password_reset(
            recipient=user_data['email'],
            user_name=user_data['name'],
            user_type=user_type,
            professional_id=professional_id,
            new_password=password
        )

        if success:
            logger.info(f"Professional password reset email sent to {user_data['email']}")
            return True
        else:
            logger.error(f"Failed to send professional password reset email to {user_data['email']}")
            return False

    except Exception as e:
        logger.error(f"Error in send_password_reset_email: {e}", exc_info=True)
        # Fallback to simple email if professional service fails
        try:
            subject = f'CVBioLabs - Password Reset Notification'
            body = f"""
            Dear {user_data['name']},

            Your {user_type} account password has been reset successfully.

            Professional ID: {professional_id}
            Email: {user_data['email']}
            New Password: {password}

            Please log in and change your password immediately.

            If you did not request this change, please contact support immediately.

            Best regards,
            CVBioLabs Team
            """
            msg = Message(
                subject=subject,
                recipients=[user_data['email']],
                body=body
            )
            mail.send(msg)
            logger.info(f"Fallback password reset email sent to {user_data['email']}")
            return True
        except Exception as fallback_error:
            logger.error(f"Fallback email also failed: {fallback_error}", exc_info=True)
            return False


@admin_bp.route('/reset-password/staff', methods=['POST'])
@limiter.limit("10 per hour")  # Increased rate limit
def reset_staff_password():
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        staff_id = request.form.get('staff_id', '').strip()
        email = request.form.get('email', '').strip().lower()
        new_password = request.form.get('new_password', '').strip()

        # Enhanced validation
        if not staff_id:
            return jsonify({'error': 'Staff ID is required'}), 400
        if not email:
            return jsonify({'error': 'Email is required'}), 400
        if not new_password:
            return jsonify({'error': 'New password is required'}), 400

        # Validate password length
        if len(new_password) < 6:
            return jsonify({'error': 'Password must be at least 6 characters long'}), 400

        hashed_password = bcrypt.hashpw(new_password.encode(), bcrypt.gensalt())

        with conn.cursor(dictionary=True) as cursor:
            cursor.execute("""
                SELECT id, name FROM admin_users 
                WHERE professional_id = %s AND email = %s AND role = 'Receptionist'
            """, (staff_id, email))
            row = cursor.fetchone()
            if not row:
                return jsonify({'error': 'Invalid receptionist ID or email'}), 400

            real_id = row['id']
            name = row['name']

            cursor.execute("""
                UPDATE admin_users 
                SET password_hash = %s 
                WHERE id = %s
            """, (hashed_password, real_id))
            conn.commit()

            # Send password reset email
            send_password_reset_email(
                {'name': name, 'email': email},
                staff_id,
                'Receptionist',
                password=new_password
            )

            return jsonify({
                'success': True,
                'message': 'Password reset successful'
            })

    except Exception as e:
        conn.rollback()
        logger.error("Error resetting receptionist password: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@admin_bp.route('/reset-password/doctor', methods=['POST'])
@limiter.limit("10 per hour")  # Increased rate limit
def reset_doctor_password():
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        doctor_id = request.form.get('doctor_id', '').strip()
        license_number = request.form.get('license_number', '').strip()
        new_password = request.form.get('new_password', '').strip()

        # Enhanced validation
        if not doctor_id:
            return jsonify({'error': 'Doctor ID is required'}), 400
        if not license_number:
            return jsonify({'error': 'License number is required'}), 400
        if not new_password:
            return jsonify({'error': 'New password is required'}), 400

        # Validate password length
        if len(new_password) < 6:
            return jsonify({'error': 'Password must be at least 6 characters long'}), 400

        hashed_password = bcrypt.hashpw(new_password.encode(), bcrypt.gensalt())
        with conn.cursor(dictionary=True) as cursor:
            cursor.execute("""
                SELECT id, name, email FROM admin_users 
                WHERE professional_id = %s AND role = 'Doctor'
            """, (doctor_id,))
            row = cursor.fetchone()
            if not row:
                return jsonify({'error': 'Invalid doctor ID'}), 400

            real_id = row['id']
            name = row['name']
            email = row['email']

            cursor.execute("""
                UPDATE admin_users 
                SET password_hash = %s 
                WHERE id = %s
            """, (hashed_password, real_id))

            cursor.execute("""
                UPDATE doctors
                SET password_hash = %s
                WHERE professional_id = %s
            """, (hashed_password, doctor_id))

            conn.commit()

            send_password_reset_email(
                {'name': name, 'email': email},
                doctor_id,
                'Doctor',
                password=new_password
            )

            return jsonify({
                'success': True,
                'message': 'Password reset successful'
            })

    except Exception as e:
        conn.rollback()
        logger.error("Error resetting doctor password: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()


@admin_bp.route('/reset-password/agent', methods=['POST'])
@limiter.limit("10 per hour")  # Increased rate limit
def reset_agent_password():
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        agent_id = request.form.get('agent_id', '').strip()
        phone = request.form.get('phone', '').strip()
        new_password = request.form.get('new_password', '').strip()

        # Enhanced validation
        if not agent_id:
            return jsonify({'error': 'Agent ID is required'}), 400
        if not phone:
            return jsonify({'error': 'Phone number is required'}), 400
        if not new_password:
            return jsonify({'error': 'New password is required'}), 400

        # Validate password length
        if len(new_password) < 6:
            return jsonify({'error': 'Password must be at least 6 characters long'}), 400

        hashed_password = bcrypt.hashpw(new_password.encode(), bcrypt.gensalt())

        with conn.cursor(dictionary=True) as cursor:
            cursor.execute("""
                SELECT id, name, email FROM pickup_agents 
                WHERE professional_id = %s AND phone = %s
            """, (agent_id, phone))
            row = cursor.fetchone()
            if not row:
                return jsonify({'error': 'Invalid agent ID or phone number'}), 400

            real_id = row['id']
            name = row['name']
            email = row['email']

            cursor.execute("""
                UPDATE pickup_agents 
                SET password_hash = %s 
                WHERE id = %s
            """, (hashed_password, real_id))
            conn.commit()

            send_password_reset_email(
                {'name': name, 'email': email},
                agent_id,
                'Pickup Agent',
                password=new_password
            )

            return jsonify({
                'success': True,
                'message': 'Password reset successful'
            })

    except Exception as e:
        conn.rollback()
        logger.error("Error resetting agent password: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@admin_bp.route('/analytics')
@login_required
@role_required('admin')
def admin_analytics():
    conn = get_db_connection()
    if not conn:
        return "Database connection failed", 500

    try:
        with conn.cursor(dictionary=True) as cursor:  # Changed to dictionary cursor
            # Revenue Analytics
            cursor.execute("""
                SELECT 
                    DATE_FORMAT(payment_date, '%Y-%m') as month,
                    CAST(SUM(amount) AS DECIMAL(10,2)) as total_revenue,
                    COUNT(*) as transaction_count
                FROM payments 
                WHERE payment_date >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                GROUP BY DATE_FORMAT(payment_date, '%Y-%m')
                ORDER BY month
            """)
            revenue_data = cursor.fetchall()

            # Test Category Distribution
            cursor.execute("""
                SELECT 
                    TestCategory,
                    COUNT(*) as test_count,
                    CAST(SUM(TestAmount) AS DECIMAL(10,2)) as total_amount
                FROM testdetails
                WHERE active = TRUE
                GROUP BY TestCategory
                ORDER BY test_count DESC
                LIMIT 10
            """)
            test_categories = cursor.fetchall()

            # Booking Status Distribution
            cursor.execute("""
                SELECT 
                    booking_status,
                    COUNT(*) as count
                FROM bookings
                GROUP BY booking_status
            """)
            booking_status = cursor.fetchall()

            # Payment Method Distribution
            cursor.execute("""
                SELECT 
                    payment_method,
                    COUNT(*) as count,
                    CAST(SUM(amount) AS DECIMAL(10,2)) as total_amount
                FROM payments
                GROUP BY payment_method
            """)
            payment_methods = cursor.fetchall()

            # Daily Bookings Trend (Last 30 days)
            cursor.execute("""
                SELECT 
                    DATE_FORMAT(booking_date, '%Y-%m-%d') as date,
                    COUNT(*) as booking_count
                FROM bookings
                WHERE booking_date >= DATE_SUB(CURDATE(), INTERVAL 30 DAY)
                GROUP BY DATE_FORMAT(booking_date, '%Y-%m-%d')
                ORDER BY date
            """)
            daily_bookings = cursor.fetchall()

            # User Growth
            cursor.execute("""
                SELECT 
                    DATE_FORMAT(created_at, '%Y-%m') as month,
                    COUNT(*) as new_users
                FROM users
                WHERE created_at >= DATE_SUB(CURDATE(), INTERVAL 12 MONTH)
                GROUP BY DATE_FORMAT(created_at, '%Y-%m')
                ORDER BY month
            """)
            user_growth = cursor.fetchall()

            # Sample Collection Status
            cursor.execute("""
                SELECT 
                    collection_status,
                    COUNT(*) as count
                FROM sample_collections
                GROUP BY collection_status
            """)
            sample_collections = cursor.fetchall()

            # Test Performance Metrics
            cursor.execute("""
                SELECT 
                    td.TestCategory,
                    COUNT(b.id) as total_bookings,
                    CAST(AVG(TIMESTAMPDIFF(HOUR, b.created_at, r.created_at)) AS DECIMAL(10,2)) as avg_turnaround_time
                FROM testdetails td
                LEFT JOIN bookings b ON td.SrNo = b.test_id
                LEFT JOIN reports r ON b.id = r.booking_id
                WHERE td.active = TRUE
                GROUP BY td.TestCategory
                ORDER BY total_bookings DESC
                LIMIT 10
            """)
            test_performance = cursor.fetchall()

            # Convert all data to JSON-serializable format
            def prepare_data(data):
                if isinstance(data, list):
                    return [prepare_data(item) for item in data]
                elif isinstance(data, dict):
                    return {k: prepare_data(v) for k, v in data.items()}
                elif isinstance(data, (datetime, date)):
                    return data.isoformat()
                elif isinstance(data, Decimal):
                    return float(data)
                return data

            # Prepare all data for JSON serialization
            revenue_data = prepare_data(revenue_data)
            test_categories = prepare_data(test_categories)
            booking_status = prepare_data(booking_status)
            payment_methods = prepare_data(payment_methods)
            daily_bookings = prepare_data(daily_bookings)
            user_growth = prepare_data(user_growth)
            sample_collections = prepare_data(sample_collections)
            test_performance = prepare_data(test_performance)

            return render_template('ADMIN/adminanalytics.html',
                                revenue_data=json.dumps(revenue_data),
                                test_categories=json.dumps(test_categories),
                                booking_status=json.dumps(booking_status),
                                payment_methods=json.dumps(payment_methods),
                                daily_bookings=json.dumps(daily_bookings),
                                user_growth=json.dumps(user_growth),
                                sample_collections=json.dumps(sample_collections),
                                test_performance=json.dumps(test_performance))

    except Exception as e:
        logger.error("Database Error: %s", str(e), exc_info=True)
        return f"Database error: {str(e)}", 500
    finally:
        conn.close()

def validate_discount_data(data):
    """Validate discount data and return any validation errors."""
    errors = []
    
    if not data.get('code'):
        errors.append('Discount code is required')
    elif not data['code'].strip():
        errors.append('Discount code cannot be empty')
    elif ' ' in data['code']:
        errors.append('Discount code cannot contain spaces')
    
    if not data.get('discount_amount'):
        errors.append('Discount amount is required')
    else:
        try:
            amount = float(data['discount_amount'])
            if amount <= 0:
                errors.append('Discount amount must be greater than 0')
        except ValueError:
            errors.append('Invalid discount amount')
    
    if not data.get('valid_until'):
        errors.append('Valid until date is required')
    else:
        try:
            valid_date = datetime.strptime(data['valid_until'], '%Y-%m-%d').date()
            if valid_date < date.today():
                errors.append('Valid until date cannot be in the past')
        except ValueError:
            errors.append('Invalid date format')
    
    if not data.get('status'):
        errors.append('Status is required')
    elif data['status'] not in ['Active', 'Expired', 'Used']:
        errors.append('Invalid status')
    
    return errors

@admin_bp.route('/discounts/save', methods=['POST'])
@csrf_exempt
@login_required
@role_required('admin')
def save_discount():
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        data = request.get_json()
        if not data:
            return jsonify({'error': 'No data provided'}), 400

        # Validate the data
        errors = validate_discount_data(data)
        if errors:
            return jsonify({'error': 'Validation failed', 'details': errors}), 400

        with conn.cursor(dictionary=True) as cursor:
            # Check if code already exists (for new discounts)
            if not data.get('id'):
                cursor.execute("SELECT id FROM coupons WHERE code = %s", (data['code'],))
                if cursor.fetchone():
                    return jsonify({'error': 'Discount code already exists'}), 400

            if data.get('id'):
                # Update existing discount
                cursor.execute("""
                    UPDATE coupons 
                    SET code = %s, discount_amount = %s, expiry_date = %s, status = %s
                    WHERE id = %s
                """, (
                    data['code'],
                    data['discount_amount'],
                    data['valid_until'],
                    data['status'],
                    data['id']
                ))
                message = 'Discount updated successfully'
            else:
                # Create new discount
                cursor.execute("""
                    INSERT INTO coupons (code, discount_amount, expiry_date, status)
                    VALUES (%s, %s, %s, %s)
                """, (
                    data['code'],
                    data['discount_amount'],
                    data['valid_until'],
                    data['status']
                ))
                message = 'Discount created successfully'

            conn.commit()

        # After successful creation or update, before returning the response:
            log_audit_action(
                session.get('user_id'),
                session.get('username'),
                "save_discount",
                f"{'Updated' if data.get('id') else 'Created'} discount code: {data['code']}",
                conn
            )

            # Get the updated/created discount - Using direct date format
            cursor.execute("""
                SELECT 
                    id,
                    code,
                    discount_amount,
                    DATE(expiry_date) as valid_until,
                    status
                FROM coupons 
                WHERE code = %s
            """, (data['code'],))
            discount = cursor.fetchone()

            return jsonify({
                'message': message,
                'success': True,
                'discount': discount
            })

    except Exception as e:
        conn.rollback()
        logger.error("Error saving discount: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@admin_bp.route('/discounts/delete/<string:code>', methods=['DELETE'])
@csrf_exempt
@login_required
@role_required('admin')
def delete_discount(code):
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Database connection failed'}), 500

    try:
        with conn.cursor(dictionary=True) as cursor:
            # Check if discount exists
            cursor.execute("SELECT id FROM coupons WHERE code = %s", (code,))
            if not cursor.fetchone():
                return jsonify({'error': 'Discount not found'}), 404

            # Delete the discount
            cursor.execute("DELETE FROM coupons WHERE code = %s", (code,))
            conn.commit()
            
            # Audit log
            log_audit_action(
                session.get('user_id'),
                session.get('username'),
                "delete_discount",
                f"Deleted discount code: {code}",
                conn
            )

            return jsonify({
                'message': 'Discount deleted successfully',
                'success': True
            })

    except Exception as e:
        conn.rollback()
        logger.error("Error deleting discount: %s", str(e), exc_info=True)
        return jsonify({'error': str(e)}), 500
    finally:
        conn.close()

@admin_bp.route('/logout')
def admin_logout():
    from flask_login import logout_user
    logout_user()
    session.clear()
    flash('You have been logged out', 'success')
    return redirect(url_for('home'))

__all__ = ['admin_bp', 'DecimalEncoder']
