<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <title>{% block title %}CVBioLabs{% endblock %}</title>
    <!--[if mso]>
    <noscript>
        <xml>
            <o:OfficeDocumentSettings>
                <o:PixelsPerInch>96</o:PixelsPerInch>
            </o:OfficeDocumentSettings>
        </xml>
    </noscript>
    <![endif]-->
    <style>
        /* Reset styles */
        body, table, td, p, a, li, blockquote {
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }
        table, td {
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            -ms-interpolation-mode: bicubic;
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
        }

        /* CVBioLabs Brand Colors */
        :root {
            --cvbio-orange: #f47c20;
            --cvbio-dark-blue: #002f6c;
            --cvbio-light-blue: #e6f7ff;
            --cvbio-gray: #f8f9fa;
            --cvbio-text: #333333;
            --cvbio-light-text: #666666;
        }

        /* Main styles */
        body {
            margin: 0 !important;
            padding: 0 !important;
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333333;
        }

        .email-container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #ffffff;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        /* Header */
        .email-header {
            background: linear-gradient(135deg, #002f6c 0%, #f47c20 100%);
            padding: 30px 20px;
            text-align: center;
        }

        .logo {
            color: #ffffff;
            font-size: 28px;
            font-weight: bold;
            text-decoration: none;
            display: inline-block;
        }

        .logo i {
            margin-right: 10px;
            font-size: 32px;
        }

        .tagline {
            color: #e6f7ff;
            font-size: 14px;
            margin-top: 5px;
            font-weight: 300;
        }

        /* Content */
        .email-content {
            padding: 40px 30px;
        }

        .greeting {
            font-size: 18px;
            color: #002f6c;
            margin-bottom: 20px;
            font-weight: 600;
        }

        .content-text {
            font-size: 16px;
            line-height: 1.6;
            color: #333333;
            margin-bottom: 20px;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: 15px 30px;
            background-color: #f47c20;
            color: #ffffff !important;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 600;
            font-size: 16px;
            text-align: center;
            margin: 20px 0;
            transition: background-color 0.3s ease;
        }

        .btn:hover {
            background-color: #e06a1a;
        }

        .btn-secondary {
            background-color: #002f6c;
        }

        .btn-secondary:hover {
            background-color: #001a3d;
        }

        /* Info boxes */
        .info-box {
            background-color: #e6f7ff;
            border-left: 4px solid #f47c20;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }

        .warning-box {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }

        .success-box {
            background-color: #d4edda;
            border-left: 4px solid #28a745;
            padding: 20px;
            margin: 20px 0;
            border-radius: 0 8px 8px 0;
        }

        /* Footer */
        .email-footer {
            background-color: #002f6c;
            color: #ffffff;
            padding: 30px 20px;
            text-align: center;
        }

        .footer-content {
            font-size: 14px;
            line-height: 1.5;
        }

        .footer-links {
            margin: 20px 0;
        }

        .footer-links a {
            color: #e6f7ff;
            text-decoration: none;
            margin: 0 15px;
            font-size: 14px;
        }

        .footer-links a:hover {
            color: #f47c20;
        }

        .social-links {
            margin: 20px 0;
        }

        .social-links a {
            color: #e6f7ff;
            font-size: 20px;
            margin: 0 10px;
            text-decoration: none;
        }

        .social-links a:hover {
            color: #f47c20;
        }

        .copyright {
            font-size: 12px;
            color: #b3d9ff;
            margin-top: 20px;
        }

        /* Responsive */
        @media only screen and (max-width: 600px) {
            .email-container {
                width: 100% !important;
                margin: 0 !important;
            }
            
            .email-content {
                padding: 20px !important;
            }
            
            .email-header {
                padding: 20px !important;
            }
            
            .logo {
                font-size: 24px !important;
            }
            
            .btn {
                display: block !important;
                width: 100% !important;
                box-sizing: border-box !important;
            }
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
            .email-container {
                background-color: #1a1a1a !important;
            }
            
            .email-content {
                background-color: #1a1a1a !important;
            }
            
            .content-text {
                color: #e0e0e0 !important;
            }
        }
    </style>
</head>
<body>
    <div class="email-container">
        <!-- Header -->
        <div class="email-header">
            <div style="text-align: center; padding: 20px;">
                <!-- Logo Section -->
                <div style="margin-bottom: 20px;">
                    {% if logo_base64 %}
                        <!-- Actual Logo Image -->
                        <img src="{{ logo_base64 }}"
                             alt="CVBioLabs Logo"
                             style="height: 80px; width: auto; max-width: 200px; display: inline-block; margin-bottom: 10px; border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.2);"
                             onerror="this.style.display='none'; this.nextElementSibling.style.display='inline-block';">

                        <!-- Fallback text logo if image fails to load -->
                        <div style="display: none; width: 80px; height: 80px; background: rgba(255,255,255,0.2); border-radius: 40px; line-height: 80px; text-align: center; margin-bottom: 15px; border: 3px solid rgba(255,255,255,0.3); box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
                            <span style="color: #ffffff; font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;">CV</span>
                        </div>
                    {% else %}
                        <!-- Text logo fallback when no base64 logo available -->
                        <div style="display: inline-block; width: 80px; height: 80px; background: rgba(255,255,255,0.2); border-radius: 40px; line-height: 80px; text-align: center; margin-bottom: 15px; border: 3px solid rgba(255,255,255,0.3); box-shadow: 0 4px 15px rgba(0,0,0,0.2);">
                            <span style="color: #ffffff; font-size: 28px; font-weight: bold; font-family: Arial, sans-serif;">CV</span>
                        </div>
                    {% endif %}
                </div>

                <!-- Company Name -->
                <div style="margin-bottom: 10px;">
                    <span style="color: #ffffff; font-size: 36px; font-weight: bold; letter-spacing: 4px; font-family: Arial, sans-serif; text-shadow: 2px 2px 4px rgba(0,0,0,0.3);">
                        <span style="color: #ffffff;">CV</span><span style="color: #e6f7ff;">BioLabs</span>
                    </span>
                </div>

                <!-- Medical Symbol -->
                <div style="margin: 15px 0;">
                    <span style="color: #e6f7ff; font-size: 24px;">⚕️</span>
                </div>

                <!-- Tagline -->
                <div style="color: #e6f7ff; font-size: 16px; font-weight: 400; font-family: Arial, sans-serif; text-shadow: 1px 1px 2px rgba(0,0,0,0.2);">
                    For a Healthy Life - Advanced Medical Diagnostics
                </div>
            </div>
        </div>

        <!-- Content -->
        <div class="email-content">
            {% block content %}{% endblock %}
        </div>

        <!-- Footer -->
        <div class="email-footer">
            <div class="footer-content">
                <div class="footer-links">
                    <a href="https://cvbiolabs.com">Website</a>
                    <a href="https://cvbiolabs.com/contact">Contact</a>
                    <a href="https://cvbiolabs.com/tests">Our Tests</a>
                    <a href="https://cvbiolabs.com/support">Support</a>
                </div>
                
                <div class="social-links">
                    <a href="#"><i class="fab fa-facebook"></i></a>
                    <a href="#"><i class="fab fa-twitter"></i></a>
                    <a href="#"><i class="fab fa-linkedin"></i></a>
                    <a href="#"><i class="fab fa-instagram"></i></a>
                </div>
                
                <div>
                    <strong>CVBioLabs</strong><br>
                    Serilingampally, Hyderabad<br>
                    Phone: +91 78936 20683<br>
                    Email: <EMAIL>
                </div>
                
                <div class="copyright">
                    © {{ current_year }} CVBioLabs. All rights reserved.<br>
                    This email was sent to {{ recipient_email }}
                </div>
            </div>
        </div>
    </div>
</body>
</html>
