# Advertisement System Testing Guide

## 🚀 Quick Start Testing

### 1. **Access Admin Panel**
1. Navigate to `/admin/dashboard`
2. Login with admin credentials
3. Click "Advertisement Management" in the sidebar

### 2. **Create Your First Advertisement**

#### **Test Case 1: Hero Banner Advertisement**
```
Title: "Special Health Package Offer"
Description: "Get 50% off on comprehensive health check-ups"
Position: Hero Banner
Type: Banner
Status: Active
Priority: 10
Target Audience: All Users
```

#### **Test Case 2: Floating Advertisement**
```
Title: "Book Now & Save!"
Description: "Limited time offer - Call now!"
Position: Sidebar
Type: Floating
Status: Active
Priority: 8
Target Audience: New Users
```

#### **Test Case 3: Popup Advertisement**
```
Title: "Welcome to CVBIOLABS"
Description: "First-time visitors get 20% discount"
Position: Sidebar
Type: Popup
Status: Active
Priority: 9
Target Audience: New Users
```

### 3. **Image Upload Testing**

#### **Upload Methods to Test:**
1. **Drag & Drop**: Drag an image file onto the upload area
2. **Browse Files**: Click "Browse Files" button and select image
3. **Crop & Resize**: Use the cropping tools to adjust image

#### **Recommended Test Images:**
- **Banner**: 1200x400px landscape image
- **Square**: 300x300px square image
- **Mobile**: 320x50px mobile banner

#### **Preset Sizes to Test:**
- Banner (1200x400) - For hero banners
- Medium (300x250) - For sidebar ads
- Leaderboard (728x90) - For header ads
- Mobile (320x50) - For mobile banners

### 4. **Frontend Display Testing**

#### **Check These Positions:**
1. **Header**: Top of the page (small banners)
2. **Hero Banner**: After main hero section (large banners)
3. **Between Sections**: Between services and process sections
4. **Footer**: Bottom of the page
5. **Floating**: Fixed position on right side
6. **Popup**: Modal that appears after 3 seconds

#### **Test Different User Types:**
1. **Guest User**: Visit home page without logging in
2. **Logged-in Patient**: Login as patient and view home page
3. **Admin User**: Login as admin and view home page

### 5. **Analytics Testing**

#### **Track These Metrics:**
1. **Impressions**: Refresh home page and check impression count
2. **Clicks**: Click on advertisements and verify click tracking
3. **Analytics Dashboard**: View performance metrics in admin panel

## 🔧 Technical Testing

### **Database Verification**
Check these tables after creating advertisements:
```sql
-- View all advertisements
SELECT * FROM advertisements;

-- View analytics data
SELECT * FROM advertisement_analytics;

-- Check impression/click counts
SELECT id, title, impression_count, click_count FROM advertisements;
```

### **File Upload Verification**
Check if images are saved correctly:
```
Location: static/uploads/advertisements/
Format: ad_YYYYMMDD_HHMMSS_uniqueid.jpg
```

### **URL Testing**
Test these endpoints:
- `/admin/advertisements` - Management interface
- `/admin/advertisements/create` - Create new ad
- `/admin/advertisements/upload-image` - Image upload
- `/ad/click/<ad_id>` - Click tracking

## 🎯 Feature Testing Checklist

### **✅ Advertisement Management**
- [ ] Create advertisement
- [ ] Edit advertisement
- [ ] Delete advertisement
- [ ] View analytics
- [ ] Change status (active/inactive)

### **✅ Image Upload & Cropping**
- [ ] Upload image via drag & drop
- [ ] Upload image via file browser
- [ ] Crop image with preset sizes
- [ ] Custom crop dimensions
- [ ] Image preview works
- [ ] Remove uploaded image

### **✅ Display Positions**
- [ ] Header advertisements display
- [ ] Hero banner advertisements display
- [ ] Between sections advertisements display
- [ ] Footer advertisements display
- [ ] Floating advertisements display
- [ ] Popup advertisements display

### **✅ Targeting & Scheduling**
- [ ] Target "All Users" works
- [ ] Target "Patients" works (login required)
- [ ] Target "New Users" works (guest only)
- [ ] Start date scheduling works
- [ ] End date scheduling works
- [ ] Priority ordering works

### **✅ Analytics & Tracking**
- [ ] Impression tracking works
- [ ] Click tracking works
- [ ] Analytics dashboard shows data
- [ ] User type tracking works
- [ ] IP address logging works

### **✅ Responsive Design**
- [ ] Desktop display (1920x1080)
- [ ] Tablet display (768x1024)
- [ ] Mobile display (375x667)
- [ ] Floating ads adjust on mobile
- [ ] Popup ads adjust on mobile

## 🐛 Common Issues & Solutions

### **Issue 1: CSRF Token Missing**
**Solution**: Ensure CSRF token is included in forms and AJAX requests

### **Issue 2: Images Not Uploading**
**Possible Causes**:
- File size too large (>5MB)
- Invalid file format
- Directory permissions
- Missing Pillow library

**Solution**: Check file size, format, and ensure `static/uploads/advertisements/` directory exists

### **Issue 3: Advertisements Not Displaying**
**Possible Causes**:
- Status is "inactive"
- Outside scheduled date range
- Wrong target audience
- Database connection issues

**Solution**: Check advertisement status, dates, and targeting settings

### **Issue 4: Click Tracking Not Working**
**Possible Causes**:
- Missing link URL
- JavaScript errors
- Database connection issues

**Solution**: Ensure link URL is set and check browser console for errors

### **Issue 5: Cropping Not Working**
**Possible Causes**:
- Cropper.js library not loaded
- JavaScript errors
- Image format issues

**Solution**: Check browser console and ensure Cropper.js CDN is accessible

## 📊 Performance Testing

### **Load Testing**
1. Create 10+ advertisements
2. Refresh home page multiple times
3. Check page load speed
4. Monitor database queries

### **Image Optimization**
1. Upload large images (>2MB)
2. Test cropping and compression
3. Verify final file sizes
4. Check image quality

### **Analytics Performance**
1. Generate 100+ impressions
2. Generate 50+ clicks
3. Check analytics query performance
4. Verify data accuracy

## 🎨 Design Testing

### **Visual Consistency**
- [ ] Advertisements match site design
- [ ] Colors are consistent
- [ ] Fonts are readable
- [ ] Images are high quality

### **User Experience**
- [ ] Advertisements don't obstruct content
- [ ] Close buttons work on popups/floating ads
- [ ] Animations are smooth
- [ ] Mobile experience is good

### **Accessibility**
- [ ] Alt text for images
- [ ] Keyboard navigation works
- [ ] Screen reader compatibility
- [ ] Color contrast is adequate

## 📝 Test Data Examples

### **Sample Advertisement Data**
```json
{
  "title": "Health Check-up Special",
  "description": "Comprehensive health screening at 50% off",
  "position": "hero_banner",
  "ad_type": "banner",
  "priority": 10,
  "status": "active",
  "target_audience": "all",
  "link_url": "https://example.com/health-packages"
}
```

### **Sample Image URLs for Testing**
- Banner: `https://via.placeholder.com/1200x400/007bff/ffffff?text=Health+Banner`
- Square: `https://via.placeholder.com/300x300/28a745/ffffff?text=Square+Ad`
- Mobile: `https://via.placeholder.com/320x50/ffc107/000000?text=Mobile+Banner`

## 🔍 Monitoring & Maintenance

### **Regular Checks**
1. **Weekly**: Review advertisement performance
2. **Monthly**: Clean up expired advertisements
3. **Quarterly**: Analyze user engagement metrics
4. **Yearly**: Review and update targeting strategies

### **Performance Metrics to Monitor**
- Click-through rates (CTR)
- Impression counts
- User engagement
- Page load impact
- Storage usage for images

This testing guide ensures comprehensive validation of the advertisement system functionality!
