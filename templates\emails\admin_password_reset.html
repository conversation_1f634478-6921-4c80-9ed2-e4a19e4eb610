{% extends "emails/base.html" %}

{% block title %}Password Reset Notification - CVBioLabs{% endblock %}

{% block content %}
<div class="greeting">
    Dear {{ user_name }},
</div>

<div class="content-text">
    This email confirms that your <strong>{{ user_type|title }}</strong> account password has been successfully reset by the CVBioLabs administration team.
</div>

<div class="warning-box" style="text-align: center; background: linear-gradient(135deg, #fff3cd 0%, #fef9e7 100%); border: 2px solid #ffc107; border-radius: 12px;">
    <div style="font-size: 18px; color: #856404; margin-bottom: 15px; font-weight: 600;">
        <i class="fas fa-key" style="color: #ffc107; font-size: 24px; margin-right: 10px;"></i>
        Password Reset Completed
    </div>
    <div style="font-size: 14px; color: #856404; margin-bottom: 10px;">
        Your account credentials have been updated
    </div>
    <div style="font-size: 12px; color: #666;">
        <i class="fas fa-calendar"></i> {{ reset_date.strftime('%B %d, %Y at %I:%M %p') }}
    </div>
</div>

<div class="content-text">
    <strong>Account Details:</strong>
</div>

<table style="width: 100%; border-collapse: collapse; margin: 20px 0; background: #f8f9fa; border-radius: 8px; overflow: hidden;">
    <tr style="background: #e9ecef;">
        <td style="padding: 12px 15px; font-weight: 600; color: #002f6c; border-bottom: 1px solid #dee2e6;">Account Type</td>
        <td style="padding: 12px 15px; border-bottom: 1px solid #dee2e6;">{{ user_type|title }}</td>
    </tr>
    <tr>
        <td style="padding: 12px 15px; font-weight: 600; color: #002f6c; border-bottom: 1px solid #dee2e6;">Professional ID</td>
        <td style="padding: 12px 15px; border-bottom: 1px solid #dee2e6; font-family: 'Courier New', monospace; font-weight: bold;">{{ professional_id }}</td>
    </tr>
    <tr style="background: #e9ecef;">
        <td style="padding: 12px 15px; font-weight: 600; color: #002f6c; border-bottom: 1px solid #dee2e6;">Email Address</td>
        <td style="padding: 12px 15px; border-bottom: 1px solid #dee2e6;">{{ user_email }}</td>
    </tr>
    <tr>
        <td style="padding: 12px 15px; font-weight: 600; color: #002f6c;">New Password</td>
        <td style="padding: 12px 15px; font-family: 'Courier New', monospace; font-weight: bold; color: #f47c20;">{{ new_password }}</td>
    </tr>
</table>

<div class="info-box">
    <div style="font-weight: 600; margin-bottom: 10px;">
        <i class="fas fa-sign-in-alt"></i> Next Steps
    </div>
    <ol style="margin: 0; padding-left: 20px;">
        <li>Use your Professional ID and the new password to log in</li>
        <li>Change your password immediately after logging in</li>
        <li>Choose a strong, unique password for better security</li>
        <li>Enable two-factor authentication if available</li>
    </ol>
</div>

<div style="text-align: center; margin: 30px 0;">
    <a href="{{ login_url }}" class="btn">
        <i class="fas fa-sign-in-alt"></i> Login to Your Account
    </a>
</div>

<div class="warning-box">
    <div style="font-weight: 600; margin-bottom: 10px;">
        <i class="fas fa-shield-alt"></i> Security Recommendations
    </div>
    <ul style="margin: 0; padding-left: 20px;">
        <li><strong>Change Password:</strong> Update your password immediately after logging in</li>
        <li><strong>Use Strong Password:</strong> Include uppercase, lowercase, numbers, and special characters</li>
        <li><strong>Keep Confidential:</strong> Never share your login credentials with anyone</li>
        <li><strong>Secure Access:</strong> Always log out when using shared computers</li>
        <li><strong>Monitor Activity:</strong> Report any suspicious account activity immediately</li>
    </ul>
</div>

<div class="content-text">
    <strong>Password Requirements:</strong>
</div>

<div class="info-box">
    <ul style="margin: 0; padding-left: 20px;">
        <li>Minimum 8 characters in length</li>
        <li>At least one uppercase letter (A-Z)</li>
        <li>At least one lowercase letter (a-z)</li>
        <li>At least one number (0-9)</li>
        <li>At least one special character (!@#$%^&*)</li>
        <li>Avoid using personal information or common words</li>
    </ul>
</div>

<div class="warning-box">
    <div style="font-weight: 600; margin-bottom: 10px;">
        <i class="fas fa-exclamation-triangle"></i> Important Security Notice
    </div>
    <div>
        If you did not request this password reset or if you believe this action was unauthorized:
        <ul style="margin: 10px 0 0 20px;">
            <li>Contact our security team immediately at <a href="mailto:<EMAIL>" style="color: #f47c20; text-decoration: none;"><EMAIL></a></li>
            <li>Call our emergency helpline: +91-XXXXXXXXXX</li>
            <li>Do not use the new password provided</li>
            <li>Report the incident for investigation</li>
        </ul>
    </div>
</div>

<div class="content-text">
    <strong>Need Help?</strong> Our technical support team is available to assist you:
</div>

<div class="content-text">
    <ul style="padding-left: 20px; color: #333;">
        <li><strong>Technical Support:</strong> <a href="mailto:<EMAIL>" style="color: #f47c20; text-decoration: none;"><EMAIL></a></li>
        <li><strong>Security Issues:</strong> <a href="mailto:<EMAIL>" style="color: #f47c20; text-decoration: none;"><EMAIL></a></li>
        <li><strong>Phone Support:</strong> +91 78936 20683 (Available 24/7)</li>
        <li><strong>Admin Portal:</strong> Available on our website</li>
    </ul>
</div>

<div class="content-text" style="margin-top: 30px;">
    Thank you for your continued service with CVBioLabs. Please ensure you follow all security protocols to protect your account.
</div>

<div class="content-text" style="color: #002f6c; font-weight: 600;">
    Best regards,<br>
    The CVBioLabs IT Administration Team
</div>
{% endblock %}
