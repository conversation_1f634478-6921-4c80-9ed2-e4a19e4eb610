{% extends "ADMIN/adminbase.html" %}

{% block title %}Advertisement Management - CVBIOLABS Admin{% endblock %}

{% block extra_css %}
<!-- Cropper.js CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.css">
<style>
    .ad-preview {
        border: 2px dashed #ddd;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        background: #f9f9f9;
        min-height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }

    .ad-preview img {
        max-width: 100%;
        max-height: 150px;
        border-radius: 4px;
        margin-bottom: 10px;
    }

    .image-upload-section {
        border: 2px dashed #ddd;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        background: #f9f9f9;
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }

    .image-upload-section:hover {
        border-color: #007bff;
        background: #f0f8ff;
    }

    .image-upload-section.dragover {
        border-color: #28a745;
        background: #f0fff0;
    }

    .upload-icon {
        font-size: 3rem;
        color: #6c757d;
        margin-bottom: 1rem;
    }

    .image-preview {
        max-width: 100%;
        max-height: 300px;
        border-radius: 8px;
        margin: 15px 0;
        display: none;
    }

    .crop-container {
        max-height: 400px;
        margin: 15px 0;
        display: none;
    }

    .crop-controls {
        margin-top: 15px;
        display: none;
    }

    .preset-sizes {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        margin-top: 10px;
    }

    .preset-size {
        padding: 5px 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.8rem;
        background: white;
    }

    .preset-size:hover {
        background: #f8f9fa;
        border-color: #007bff;
    }

    .preset-size.active {
        background: #007bff;
        color: white;
        border-color: #007bff;
    }
    
    .position-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
    }
    
    .analytics-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .analytics-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .ad-status-active { background-color: #28a745; }
    .ad-status-inactive { background-color: #6c757d; }
    .ad-status-scheduled { background-color: #ffc107; color: #000; }
    .ad-status-expired { background-color: #dc3545; }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Header -->
    <div class="dashboard-header">
        <h1 class="dashboard-title">
            <i class="fas fa-bullhorn"></i>
            Advertisement Management
        </h1>
        <div class="header-actions">
            <button class="modern-btn modern-btn-primary" data-bs-toggle="modal" data-bs-target="#createAdModal">
                <i class="fas fa-plus"></i>
                <span>Create Advertisement</span>
            </button>
            <button class="modern-btn modern-btn-secondary" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i>
                <span>Refresh</span>
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="modern-stat-card stat-blue">
                <div class="stat-header">
                    <h6 class="stat-title">Total Ads</h6>
                    <div class="stat-icon">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                </div>
                <div class="stat-value">{{ stats.total_ads or 0 }}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="modern-stat-card stat-green">
                <div class="stat-header">
                    <h6 class="stat-title">Active Ads</h6>
                    <div class="stat-icon">
                        <i class="fas fa-play-circle"></i>
                    </div>
                </div>
                <div class="stat-value">{{ stats.active_ads or 0 }}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="modern-stat-card stat-orange">
                <div class="stat-header">
                    <h6 class="stat-title">Scheduled</h6>
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="stat-value">{{ stats.scheduled_ads or 0 }}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="modern-stat-card stat-red">
                <div class="stat-header">
                    <h6 class="stat-title">Inactive</h6>
                    <div class="stat-icon">
                        <i class="fas fa-pause-circle"></i>
                    </div>
                </div>
                <div class="stat-value">{{ stats.inactive_ads or 0 }}</div>
            </div>
        </div>
    </div>

    <!-- Advertisements Table -->
    <div class="modern-card">
        <div class="card-header">
            <h5 class="card-title">
                <i class="fas fa-list"></i>
                All Advertisements
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="advertisementsTable">
                    <thead>
                        <tr>
                            <th>Preview</th>
                            <th>Title</th>
                            <th>Position</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Priority</th>
                            <th>Analytics</th>
                            <th>Schedule</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for ad in advertisements %}
                        <tr>
                            <td>
                                <div class="ad-preview" style="width: 100px; height: 60px; min-height: auto;">
                                    {% if ad.image_url %}
                                        <img src="{{ ad.image_url }}" alt="Ad Preview" style="max-height: 50px;">
                                    {% else %}
                                        <i class="fas fa-image text-muted"></i>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <strong>{{ ad.title }}</strong>
                                {% if ad.description %}
                                    <br><small class="text-muted">{{ ad.description[:50] }}{% if ad.description|length > 50 %}...{% endif %}</small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="position-badge badge bg-info">{{ ad.position.replace('_', ' ').title() }}</span>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ ad.ad_type.title() }}</span>
                            </td>
                            <td>
                                <span class="badge ad-status-{{ ad.status }}">{{ ad.status.title() }}</span>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ ad.priority }}</span>
                            </td>
                            <td>
                                <small>
                                    <i class="fas fa-eye"></i> {{ ad.impressions or 0 }}<br>
                                    <i class="fas fa-mouse-pointer"></i> {{ ad.clicks or 0 }}
                                </small>
                            </td>
                            <td>
                                <small>
                                    {% if ad.start_date %}
                                        <strong>Start:</strong> {{ ad.start_date.strftime('%Y-%m-%d') }}<br>
                                    {% endif %}
                                    {% if ad.end_date %}
                                        <strong>End:</strong> {{ ad.end_date.strftime('%Y-%m-%d') }}
                                    {% endif %}
                                </small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="editAdvertisement({{ ad.id }})" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteAdvertisement({{ ad.id }})" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Create Advertisement Modal -->
<div class="modal fade" id="createAdModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Advertisement</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createAdForm">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}"/>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Title *</label>
                                <input type="text" class="form-control" name="title" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Description</label>
                                <textarea class="form-control" name="description" rows="3"></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Advertisement Image</label>
                                <div class="image-upload-section" id="imageUploadSection">
                                    <div class="upload-content">
                                        <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                        <p>Drag & drop an image here or click to browse</p>
                                        <input type="file" id="imageInput" accept="image/*" style="display: none;">
                                        <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('imageInput').click()">
                                            <i class="fas fa-folder-open"></i> Browse Files
                                        </button>
                                    </div>
                                </div>

                                <!-- Image Preview -->
                                <img id="imagePreview" class="image-preview" alt="Image Preview">

                                <!-- Crop Container -->
                                <div id="cropContainer" class="crop-container">
                                    <img id="cropImage" style="max-width: 100%;">
                                </div>

                                <!-- Crop Controls -->
                                <div id="cropControls" class="crop-controls">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label class="form-label">Preset Sizes</label>
                                            <div class="preset-sizes">
                                                <span class="preset-size" data-width="1200" data-height="400">Banner (1200x400)</span>
                                                <span class="preset-size" data-width="300" data-height="250">Medium (300x250)</span>
                                                <span class="preset-size" data-width="728" data-height="90">Leaderboard (728x90)</span>
                                                <span class="preset-size" data-width="320" data-height="50">Mobile (320x50)</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="row">
                                                <div class="col-6">
                                                    <label class="form-label">Width</label>
                                                    <input type="number" id="cropWidth" class="form-control" placeholder="Width">
                                                </div>
                                                <div class="col-6">
                                                    <label class="form-label">Height</label>
                                                    <input type="number" id="cropHeight" class="form-control" placeholder="Height">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <button type="button" class="btn btn-success" onclick="applyCrop()">
                                            <i class="fas fa-crop"></i> Apply Crop
                                        </button>
                                        <button type="button" class="btn btn-secondary" onclick="resetCrop()">
                                            <i class="fas fa-undo"></i> Reset
                                        </button>
                                        <button type="button" class="btn btn-danger" onclick="removeImage()">
                                            <i class="fas fa-trash"></i> Remove Image
                                        </button>
                                    </div>
                                </div>

                                <!-- Hidden field for image URL -->
                                <input type="hidden" name="image_url" id="finalImageUrl">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Link URL</label>
                                <input type="url" class="form-control" name="link_url" placeholder="https://example.com">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Position *</label>
                                <select class="form-select" name="position" required>
                                    <option value="hero_banner">Hero Banner</option>
                                    <option value="sidebar">Sidebar</option>
                                    <option value="header">Header</option>
                                    <option value="footer">Footer</option>
                                    <option value="between_sections">Between Sections</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Ad Type *</label>
                                <select class="form-select" name="ad_type" required>
                                    <option value="banner">Banner</option>
                                    <option value="popup">Popup</option>
                                    <option value="inline">Inline</option>
                                    <option value="floating">Floating</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Priority</label>
                                <input type="number" class="form-control" name="priority" value="1" min="1" max="10">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Status *</label>
                                <select class="form-select" name="status" required>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="scheduled">Scheduled</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Target Audience</label>
                                <select class="form-select" name="target_audience">
                                    <option value="all">All Users</option>
                                    <option value="patients">Patients Only</option>
                                    <option value="doctors">Doctors Only</option>
                                    <option value="new_users">New Users</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Start Date</label>
                                <input type="datetime-local" class="form-control" name="start_date">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">End Date</label>
                                <input type="datetime-local" class="form-control" name="end_date">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveAdvertisement()">Create Advertisement</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Cropper.js JavaScript -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.js"></script>
<script>
let cropper = null;
let uploadedImageUrl = null;

// Initialize DataTable
$(document).ready(function() {
    $('#advertisementsTable').DataTable({
        responsive: true,
        order: [[5, 'desc']], // Sort by priority
        columnDefs: [
            { orderable: false, targets: [0, 8] } // Disable sorting for preview and actions
        ]
    });

    // Initialize image upload functionality
    initializeImageUpload();
});

// Image Upload Functionality
function initializeImageUpload() {
    const imageInput = document.getElementById('imageInput');
    const uploadSection = document.getElementById('imageUploadSection');
    const imagePreview = document.getElementById('imagePreview');
    const cropContainer = document.getElementById('cropContainer');
    const cropControls = document.getElementById('cropControls');
    const cropImage = document.getElementById('cropImage');

    // File input change event
    imageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            handleImageUpload(file);
        }
    });

    // Drag and drop functionality
    uploadSection.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadSection.classList.add('dragover');
    });

    uploadSection.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadSection.classList.remove('dragover');
    });

    uploadSection.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadSection.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleImageUpload(files[0]);
        }
    });

    // Preset size buttons
    document.querySelectorAll('.preset-size').forEach(button => {
        button.addEventListener('click', function() {
            document.querySelectorAll('.preset-size').forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            const width = this.dataset.width;
            const height = this.dataset.height;
            document.getElementById('cropWidth').value = width;
            document.getElementById('cropHeight').value = height;

            if (cropper) {
                const aspectRatio = width / height;
                cropper.setAspectRatio(aspectRatio);
            }
        });
    });
}

// Handle image upload
function handleImageUpload(file) {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        showAlert('danger', 'Please select a valid image file (JPEG, PNG, GIF, WEBP)');
        return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
        showAlert('danger', 'File size must be less than 5MB');
        return;
    }

    // Show image preview
    const reader = new FileReader();
    reader.onload = function(e) {
        const imagePreview = document.getElementById('imagePreview');
        const cropImage = document.getElementById('cropImage');
        const cropContainer = document.getElementById('cropContainer');
        const cropControls = document.getElementById('cropControls');

        imagePreview.src = e.target.result;
        cropImage.src = e.target.result;

        imagePreview.style.display = 'block';
        cropContainer.style.display = 'block';
        cropControls.style.display = 'block';

        // Initialize cropper
        if (cropper) {
            cropper.destroy();
        }

        cropper = new Cropper(cropImage, {
            aspectRatio: NaN, // Free aspect ratio
            viewMode: 1,
            autoCropArea: 1,
            responsive: true,
            background: false,
            guides: true,
            center: true,
            highlight: true,
            cropBoxMovable: true,
            cropBoxResizable: true,
            toggleDragModeOnDblclick: false
        });
    };

    reader.readAsDataURL(file);
}

// Apply crop and upload
function applyCrop() {
    if (!cropper) {
        showAlert('danger', 'No image to crop');
        return;
    }

    const canvas = cropper.getCroppedCanvas({
        width: document.getElementById('cropWidth').value || undefined,
        height: document.getElementById('cropHeight').value || undefined,
        imageSmoothingQuality: 'high'
    });

    canvas.toBlob(function(blob) {
        const formData = new FormData();
        formData.append('image', blob, 'cropped_image.jpg');

        // Add crop data
        const cropData = {
            width: document.getElementById('cropWidth').value,
            height: document.getElementById('cropHeight').value
        };
        formData.append('crop_data', JSON.stringify(cropData));

        // Show loading
        showAlert('info', 'Uploading image...');

        fetch('/admin/advertisements/upload-image', {
            method: 'POST',
            headers: {
                'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
            },
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                uploadedImageUrl = data.image_url;
                document.getElementById('finalImageUrl').value = data.image_url;
                showAlert('success', 'Image uploaded successfully!');

                // Update preview
                document.getElementById('imagePreview').src = data.image_url;
            } else {
                showAlert('danger', data.error || 'Failed to upload image');
            }
        })
        .catch(error => {
            showAlert('danger', 'Error uploading image');
            console.error('Error:', error);
        });
    }, 'image/jpeg', 0.9);
}

// Reset crop
function resetCrop() {
    if (cropper) {
        cropper.reset();
    }
    document.getElementById('cropWidth').value = '';
    document.getElementById('cropHeight').value = '';
    document.querySelectorAll('.preset-size').forEach(b => b.classList.remove('active'));
}

// Remove image
function removeImage() {
    if (cropper) {
        cropper.destroy();
        cropper = null;
    }

    document.getElementById('imagePreview').style.display = 'none';
    document.getElementById('cropContainer').style.display = 'none';
    document.getElementById('cropControls').style.display = 'none';
    document.getElementById('finalImageUrl').value = '';
    document.getElementById('imageInput').value = '';
    uploadedImageUrl = null;
}

// Save Advertisement
function saveAdvertisement() {
    const form = document.getElementById('createAdForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());

    // Validate required fields
    if (!data.title || !data.position || !data.ad_type || !data.status) {
        showAlert('danger', 'Please fill in all required fields');
        return;
    }

    // Use uploaded image URL if available
    if (uploadedImageUrl) {
        data.image_url = uploadedImageUrl;
    }

    fetch('/admin/advertisements/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': document.querySelector('input[name="csrf_token"]').value
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert('success', data.message);
            $('#createAdModal').modal('hide');
            resetForm();
            location.reload();
        } else {
            showAlert('danger', data.error || 'Failed to create advertisement');
        }
    })
    .catch(error => {
        showAlert('danger', 'Error creating advertisement');
        console.error('Error:', error);
    });
}

// Reset form
function resetForm() {
    document.getElementById('createAdForm').reset();
    removeImage();
    uploadedImageUrl = null;
}

// Edit Advertisement
function editAdvertisement(adId) {
    // Implementation for editing advertisement
    showAlert('info', 'Edit functionality will be implemented');
}

// Delete Advertisement
function deleteAdvertisement(adId) {
    if (confirm('Are you sure you want to delete this advertisement?')) {
        fetch(`/admin/advertisements/delete/${adId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.message) {
                showAlert('success', data.message);
                location.reload();
            } else {
                showAlert('danger', data.error || 'Failed to delete advertisement');
            }
        })
        .catch(error => {
            showAlert('danger', 'Error deleting advertisement');
            console.error('Error:', error);
        });
    }
}

// Show Alert
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.dashboard-container').insertBefore(alertDiv, document.querySelector('.dashboard-container').firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}
