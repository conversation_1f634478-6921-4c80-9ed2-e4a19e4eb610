{% extends "ADMIN/adminbase.html" %}

{% block title %}Advertisement Management - CVBIOLABS Admin{% endblock %}

{% block extra_css %}
<!-- Cropper.js CSS -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.css">
<style>
    .ad-preview {
        border: 2px dashed #ddd;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        background: #f9f9f9;
        min-height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }

    .ad-preview img {
        max-width: 100%;
        max-height: 150px;
        border-radius: 4px;
        margin-bottom: 10px;
    }

    .image-upload-section {
        border: 2px dashed #ddd;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        background: #f9f9f9;
        margin-bottom: 15px;
        transition: all 0.3s ease;
    }

    .image-upload-section:hover {
        border-color: #007bff;
        background: #f0f8ff;
    }

    .image-upload-section.dragover {
        border-color: #28a745;
        background: #f0fff0;
    }

    .upload-icon {
        font-size: 3rem;
        color: #6c757d;
        margin-bottom: 1rem;
    }

    .image-preview {
        max-width: 100%;
        max-height: 300px;
        border-radius: 8px;
        margin: 15px 0;
        display: none;
    }

    .crop-container {
        max-height: 400px;
        margin: 15px 0;
        display: none;
    }

    .crop-controls {
        margin-top: 15px;
        display: none;
    }

    .preset-sizes {
        display: flex;
        gap: 10px;
        flex-wrap: wrap;
        margin-top: 10px;
    }

    .preset-size {
        padding: 5px 10px;
        border: 1px solid #ddd;
        border-radius: 4px;
        cursor: pointer;
        font-size: 0.8rem;
        background: white;
    }

    .preset-size:hover {
        background: #f8f9fa;
        border-color: #007bff;
    }

    .preset-size.active {
        background: #007bff;
        color: white;
        border-color: #007bff;
    }

    /* Admin Dashboard Styling */
    .border-left-primary {
        border-left: 0.25rem solid #4e73df !important;
    }

    .border-left-success {
        border-left: 0.25rem solid #1cc88a !important;
    }

    .border-left-warning {
        border-left: 0.25rem solid #f6c23e !important;
    }

    .border-left-danger {
        border-left: 0.25rem solid #e74a3b !important;
    }

    .text-xs {
        font-size: 0.7rem;
    }

    .font-weight-bold {
        font-weight: 700 !important;
    }

    .text-gray-800 {
        color: #5a5c69 !important;
    }

    .text-gray-300 {
        color: #dddfeb !important;
    }

    .ad-preview-small {
        width: 60px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        border: 1px solid #e3e6f0;
        border-radius: 0.35rem;
        background: #f8f9fc;
    }

    .badge-sm {
        font-size: 0.65rem;
        padding: 0.2rem 0.4rem;
    }

    .table th {
        border-top: none;
        font-weight: 600;
        color: #5a5c69;
        background-color: #f8f9fc;
    }

    .table td {
        vertical-align: middle;
    }

    .btn-group-vertical .btn {
        width: 100%;
    }

    .card {
        box-shadow: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15) !important;
        border: 1px solid #e3e6f0;
    }

    .card-header {
        background-color: #f8f9fc;
        border-bottom: 1px solid #e3e6f0;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Modern Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card border-0 shadow-sm">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2 class="mb-1 text-primary">
                                <i class="fas fa-bullhorn me-2"></i>
                                Advertisement Management
                            </h2>
                            <p class="text-muted mb-0">Create and manage banner advertisements for your website</p>
                        </div>
                        <div class="d-flex gap-2">
                            <button class="btn btn-primary btn-lg" data-bs-toggle="modal" data-bs-target="#createAdModal">
                                <i class="fas fa-plus me-2"></i>Create Advertisement
                            </button>
                            <button class="btn btn-success btn-lg" data-bs-toggle="collapse" data-bs-target="#quickCreateForm">
                                <i class="fas fa-bolt me-2"></i>Quick Create
                            </button>
                            <button class="btn btn-outline-secondary" onclick="location.reload()">
                                <i class="fas fa-sync-alt me-2"></i>Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Create Form -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="collapse" id="quickCreateForm">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="fas fa-bolt me-2"></i>Quick Advertisement Creation
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" action="/admin/advertisements/create" class="row g-3">
                            <div class="col-md-4">
                                <label class="form-label">Title *</label>
                                <input type="text" class="form-control" name="title" required placeholder="Advertisement Title">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Position *</label>
                                <select class="form-select" name="position" required>
                                    <option value="">Select Position</option>
                                    <option value="hero_banner">Hero Banner</option>
                                    <option value="sidebar">Sidebar</option>
                                    <option value="header">Header</option>
                                    <option value="footer">Footer</option>
                                    <option value="between_sections">Between Sections</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Type *</label>
                                <select class="form-select" name="ad_type" required>
                                    <option value="">Select Type</option>
                                    <option value="banner">Banner</option>
                                    <option value="popup">Popup</option>
                                    <option value="inline">Inline</option>
                                    <option value="floating">Floating</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">Status *</label>
                                <select class="form-select" name="status" required>
                                    <option value="">Select Status</option>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="scheduled">Scheduled</option>
                                </select>
                            </div>
                            <div class="col-md-1">
                                <label class="form-label">Priority</label>
                                <input type="number" class="form-control" name="priority" value="1" min="1" max="10">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Description</label>
                                <textarea class="form-control" name="description" rows="2" placeholder="Advertisement description"></textarea>
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Image URL</label>
                                <input type="url" class="form-control" name="image_url" placeholder="https://example.com/image.jpg">
                            </div>
                            <div class="col-md-6">
                                <label class="form-label">Link URL</label>
                                <input type="url" class="form-control" name="link_url" placeholder="https://example.com">
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">Target Audience</label>
                                <select class="form-select" name="target_audience">
                                    <option value="all">All Users</option>
                                    <option value="patients">Patients Only</option>
                                    <option value="doctors">Doctors Only</option>
                                    <option value="new_users">New Users</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-grid">
                                    <button type="submit" class="btn btn-success">
                                        <i class="fas fa-plus me-2"></i>Create Advertisement
                                    </button>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-primary shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">Total Advertisements</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.total_ads or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-bullhorn fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-success shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-success text-uppercase mb-1">Active Ads</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.active_ads or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-play-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-warning shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">Scheduled</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.scheduled_ads or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-clock fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6 mb-4">
            <div class="card border-left-danger shadow h-100 py-2">
                <div class="card-body">
                    <div class="row no-gutters align-items-center">
                        <div class="col mr-2">
                            <div class="text-xs font-weight-bold text-danger text-uppercase mb-1">Inactive</div>
                            <div class="h5 mb-0 font-weight-bold text-gray-800">{{ stats.inactive_ads or 0 }}</div>
                        </div>
                        <div class="col-auto">
                            <i class="fas fa-pause-circle fa-2x text-gray-300"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Advertisements Table -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow mb-4">
                <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                    <h6 class="m-0 font-weight-bold text-primary">
                        <i class="fas fa-list me-2"></i>All Advertisements
                    </h6>
                    <div class="dropdown no-arrow">
                        <a class="dropdown-toggle" href="#" role="button" id="dropdownMenuLink" data-toggle="dropdown">
                            <i class="fas fa-ellipsis-v fa-sm fa-fw text-gray-400"></i>
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-bordered" id="advertisementsTable" width="100%" cellspacing="0">
                            <thead>
                                <tr>
                                    <th width="80">Preview</th>
                                    <th>Advertisement Details</th>
                                    <th width="120">Position</th>
                                    <th width="100">Type</th>
                                    <th width="100">Status</th>
                                    <th width="120">Analytics</th>
                                    <th width="150">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if advertisements %}
                                    {% for ad in advertisements %}
                                    <tr>
                                        <td class="text-center">
                                            <div class="ad-preview-small">
                                                {% if ad.image_url %}
                                                    <img src="{{ ad.image_url }}" alt="Ad Preview" class="img-thumbnail" style="max-width: 60px; max-height: 40px;">
                                                {% else %}
                                                    <div class="bg-light p-2 rounded">
                                                        <i class="fas fa-image text-muted"></i>
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="d-flex flex-column">
                                                <h6 class="mb-1 font-weight-bold">{{ ad.title }}</h6>
                                                {% if ad.description %}
                                                    <small class="text-muted">{{ ad.description[:80] }}{% if ad.description|length > 80 %}...{% endif %}</small>
                                                {% endif %}
                                                <div class="mt-1">
                                                    <span class="badge badge-primary badge-sm">Priority: {{ ad.priority }}</span>
                                                    {% if ad.target_audience != 'all' %}
                                                        <span class="badge badge-info badge-sm">{{ ad.target_audience.title() }}</span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">{{ ad.position.replace('_', ' ').title() }}</span>
                                        </td>
                                        <td>
                                            <span class="badge badge-secondary">{{ ad.ad_type.title() }}</span>
                                        </td>
                                        <td>
                                            {% if ad.status == 'active' %}
                                                <span class="badge badge-success">Active</span>
                                            {% elif ad.status == 'inactive' %}
                                                <span class="badge badge-secondary">Inactive</span>
                                            {% elif ad.status == 'scheduled' %}
                                                <span class="badge badge-warning">Scheduled</span>
                                            {% else %}
                                                <span class="badge badge-danger">Expired</span>
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="text-center">
                                                <div class="small">
                                                    <i class="fas fa-eye text-primary"></i> {{ ad.impressions or 0 }}
                                                </div>
                                                <div class="small">
                                                    <i class="fas fa-mouse-pointer text-success"></i> {{ ad.clicks or 0 }}
                                                </div>
                                                {% if (ad.impressions or 0) > 0 %}
                                                    <div class="small text-muted">
                                                        CTR: {{ "%.1f"|format(((ad.clicks or 0) / ad.impressions * 100)) }}%
                                                    </div>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td>
                                            <div class="btn-group-vertical btn-group-sm" role="group">
                                                <button class="btn btn-outline-primary btn-sm mb-1" onclick="editAdvertisement({{ ad.id }})" title="Edit">
                                                    <i class="fas fa-edit me-1"></i>Edit
                                                </button>
                                                <button class="btn btn-outline-danger btn-sm" onclick="deleteAdvertisement({{ ad.id }})" title="Delete">
                                                    <i class="fas fa-trash me-1"></i>Delete
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="7" class="text-center py-4">
                                            <div class="text-muted">
                                                <i class="fas fa-bullhorn fa-3x mb-3"></i>
                                                <h5>No advertisements found</h5>
                                                <p>Create your first advertisement to get started!</p>
                                                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#createAdModal">
                                                    <i class="fas fa-plus me-2"></i>Create Advertisement
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Advertisement Modal -->
<div class="modal fade" id="createAdModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Advertisement</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createAdForm" method="POST" action="/admin/advertisements/create">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Title *</label>
                                <input type="text" class="form-control" name="title" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Description</label>
                                <textarea class="form-control" name="description" rows="3"></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Advertisement Image</label>
                                <div class="image-upload-section" id="imageUploadSection">
                                    <div class="upload-content">
                                        <i class="fas fa-cloud-upload-alt upload-icon"></i>
                                        <p>Drag & drop an image here or click to browse</p>
                                        <input type="file" id="imageInput" accept="image/*" style="display: none;">
                                        <button type="button" class="btn btn-outline-primary" onclick="document.getElementById('imageInput').click()">
                                            <i class="fas fa-folder-open"></i> Browse Files
                                        </button>
                                    </div>
                                </div>

                                <!-- Image Preview -->
                                <img id="imagePreview" class="image-preview" alt="Image Preview">

                                <!-- Crop Container -->
                                <div id="cropContainer" class="crop-container">
                                    <img id="cropImage" style="max-width: 100%;">
                                </div>

                                <!-- Crop Controls -->
                                <div id="cropControls" class="crop-controls">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <label class="form-label">Preset Sizes</label>
                                            <div class="preset-sizes">
                                                <span class="preset-size" data-width="1200" data-height="400">Banner (1200x400)</span>
                                                <span class="preset-size" data-width="300" data-height="250">Medium (300x250)</span>
                                                <span class="preset-size" data-width="728" data-height="90">Leaderboard (728x90)</span>
                                                <span class="preset-size" data-width="320" data-height="50">Mobile (320x50)</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="row">
                                                <div class="col-6">
                                                    <label class="form-label">Width</label>
                                                    <input type="number" id="cropWidth" class="form-control" placeholder="Width">
                                                </div>
                                                <div class="col-6">
                                                    <label class="form-label">Height</label>
                                                    <input type="number" id="cropHeight" class="form-control" placeholder="Height">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <button type="button" class="btn btn-success" onclick="applyCrop()">
                                            <i class="fas fa-crop"></i> Apply Crop
                                        </button>
                                        <button type="button" class="btn btn-secondary" onclick="resetCrop()">
                                            <i class="fas fa-undo"></i> Reset
                                        </button>
                                        <button type="button" class="btn btn-danger" onclick="removeImage()">
                                            <i class="fas fa-trash"></i> Remove Image
                                        </button>
                                    </div>
                                </div>

                                <!-- Hidden field for image URL -->
                                <input type="hidden" name="image_url" id="finalImageUrl">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Link URL</label>
                                <input type="url" class="form-control" name="link_url" placeholder="https://example.com">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Position *</label>
                                <select class="form-select" name="position" required>
                                    <option value="hero_banner">Hero Banner</option>
                                    <option value="sidebar">Sidebar</option>
                                    <option value="header">Header</option>
                                    <option value="footer">Footer</option>
                                    <option value="between_sections">Between Sections</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Ad Type *</label>
                                <select class="form-select" name="ad_type" required>
                                    <option value="banner">Banner</option>
                                    <option value="popup">Popup</option>
                                    <option value="inline">Inline</option>
                                    <option value="floating">Floating</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Priority</label>
                                <input type="number" class="form-control" name="priority" value="1" min="1" max="10">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Status *</label>
                                <select class="form-select" name="status" required>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="scheduled">Scheduled</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Target Audience</label>
                                <select class="form-select" name="target_audience">
                                    <option value="all">All Users</option>
                                    <option value="patients">Patients Only</option>
                                    <option value="doctors">Doctors Only</option>
                                    <option value="new_users">New Users</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Start Date</label>
                                <input type="datetime-local" class="form-control" name="start_date">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">End Date</label>
                                <input type="datetime-local" class="form-control" name="end_date">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="submit" class="btn btn-primary">Create Advertisement</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- Cropper.js JavaScript -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/cropperjs/1.5.12/cropper.min.js"></script>
<script>
let cropper = null;
let uploadedImageUrl = null;

// Initialize DataTable
$(document).ready(function() {
    $('#advertisementsTable').DataTable({
        responsive: true,
        order: [[5, 'desc']], // Sort by priority
        columnDefs: [
            { orderable: false, targets: [0, 8] } // Disable sorting for preview and actions
        ]
    });

    // Initialize image upload functionality
    initializeImageUpload();
});

// Image Upload Functionality
function initializeImageUpload() {
    const imageInput = document.getElementById('imageInput');
    const uploadSection = document.getElementById('imageUploadSection');
    const imagePreview = document.getElementById('imagePreview');
    const cropContainer = document.getElementById('cropContainer');
    const cropControls = document.getElementById('cropControls');
    const cropImage = document.getElementById('cropImage');

    // File input change event
    imageInput.addEventListener('change', function(e) {
        const file = e.target.files[0];
        if (file) {
            handleImageUpload(file);
        }
    });

    // Drag and drop functionality
    uploadSection.addEventListener('dragover', function(e) {
        e.preventDefault();
        uploadSection.classList.add('dragover');
    });

    uploadSection.addEventListener('dragleave', function(e) {
        e.preventDefault();
        uploadSection.classList.remove('dragover');
    });

    uploadSection.addEventListener('drop', function(e) {
        e.preventDefault();
        uploadSection.classList.remove('dragover');
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleImageUpload(files[0]);
        }
    });

    // Preset size buttons
    document.querySelectorAll('.preset-size').forEach(button => {
        button.addEventListener('click', function() {
            document.querySelectorAll('.preset-size').forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            const width = this.dataset.width;
            const height = this.dataset.height;
            document.getElementById('cropWidth').value = width;
            document.getElementById('cropHeight').value = height;

            if (cropper) {
                const aspectRatio = width / height;
                cropper.setAspectRatio(aspectRatio);
            }
        });
    });
}

// Handle image upload
function handleImageUpload(file) {
    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
        showAlert('danger', 'Please select a valid image file (JPEG, PNG, GIF, WEBP)');
        return;
    }

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
        showAlert('danger', 'File size must be less than 5MB');
        return;
    }

    // Show image preview
    const reader = new FileReader();
    reader.onload = function(e) {
        const imagePreview = document.getElementById('imagePreview');
        const cropImage = document.getElementById('cropImage');
        const cropContainer = document.getElementById('cropContainer');
        const cropControls = document.getElementById('cropControls');

        imagePreview.src = e.target.result;
        cropImage.src = e.target.result;

        imagePreview.style.display = 'block';
        cropContainer.style.display = 'block';
        cropControls.style.display = 'block';

        // Initialize cropper
        if (cropper) {
            cropper.destroy();
        }

        cropper = new Cropper(cropImage, {
            aspectRatio: NaN, // Free aspect ratio
            viewMode: 1,
            autoCropArea: 1,
            responsive: true,
            background: false,
            guides: true,
            center: true,
            highlight: true,
            cropBoxMovable: true,
            cropBoxResizable: true,
            toggleDragModeOnDblclick: false
        });
    };

    reader.readAsDataURL(file);
}

// Apply crop and upload
function applyCrop() {
    if (!cropper) {
        showAlert('danger', 'No image to crop');
        return;
    }

    const canvas = cropper.getCroppedCanvas({
        width: document.getElementById('cropWidth').value || undefined,
        height: document.getElementById('cropHeight').value || undefined,
        imageSmoothingQuality: 'high'
    });

    canvas.toBlob(function(blob) {
        const formData = new FormData();
        formData.append('image', blob, 'cropped_image.jpg');

        // Add crop data
        const cropData = {
            width: document.getElementById('cropWidth').value,
            height: document.getElementById('cropHeight').value
        };
        formData.append('crop_data', JSON.stringify(cropData));

        // Show loading
        showAlert('info', 'Uploading image...');

        fetch('/admin/advertisements/upload-image', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                uploadedImageUrl = data.image_url;
                document.getElementById('finalImageUrl').value = data.image_url;
                showAlert('success', 'Image uploaded successfully!');

                // Update preview
                document.getElementById('imagePreview').src = data.image_url;
            } else {
                showAlert('danger', data.error || 'Failed to upload image');
            }
        })
        .catch(error => {
            showAlert('danger', 'Error uploading image');
            console.error('Error:', error);
        });
    }, 'image/jpeg', 0.9);
}

// Reset crop
function resetCrop() {
    if (cropper) {
        cropper.reset();
    }
    document.getElementById('cropWidth').value = '';
    document.getElementById('cropHeight').value = '';
    document.querySelectorAll('.preset-size').forEach(b => b.classList.remove('active'));
}

// Remove image
function removeImage() {
    if (cropper) {
        cropper.destroy();
        cropper = null;
    }

    document.getElementById('imagePreview').style.display = 'none';
    document.getElementById('cropContainer').style.display = 'none';
    document.getElementById('cropControls').style.display = 'none';
    document.getElementById('finalImageUrl').value = '';
    document.getElementById('imageInput').value = '';
    uploadedImageUrl = null;
}

// Save Advertisement
function saveAdvertisement() {
    const form = document.getElementById('createAdForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());

    // Validate required fields
    if (!data.title || !data.position || !data.ad_type || !data.status) {
        showAlert('danger', 'Please fill in all required fields');
        return;
    }

    // Use uploaded image URL if available
    if (uploadedImageUrl) {
        data.image_url = uploadedImageUrl;
    }

    fetch('/admin/advertisements/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert('success', data.message);
            $('#createAdModal').modal('hide');
            resetForm();
            location.reload();
        } else {
            showAlert('danger', data.error || 'Failed to create advertisement');
        }
    })
    .catch(error => {
        showAlert('danger', 'Error creating advertisement');
        console.error('Error:', error);
    });
}

// Reset form
function resetForm() {
    document.getElementById('createAdForm').reset();
    removeImage();
    uploadedImageUrl = null;
}

// Edit Advertisement
function editAdvertisement(adId) {
    // Implementation for editing advertisement
    showAlert('info', 'Edit functionality will be implemented');
}

// Delete Advertisement
function deleteAdvertisement(adId) {
    if (confirm('Are you sure you want to delete this advertisement?')) {
        fetch(`/admin/advertisements/delete/${adId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.message) {
                showAlert('success', data.message);
                location.reload();
            } else {
                showAlert('danger', data.error || 'Failed to delete advertisement');
            }
        })
        .catch(error => {
            showAlert('danger', 'Error deleting advertisement');
            console.error('Error:', error);
        });
    }
}

// Show Alert
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.dashboard-container').insertBefore(alertDiv, document.querySelector('.dashboard-container').firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}
