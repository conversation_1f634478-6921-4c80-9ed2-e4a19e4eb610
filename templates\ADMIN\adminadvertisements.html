{% extends "ADMIN/adminbase.html" %}

{% block title %}Advertisement Management - CVBIOLABS Admin{% endblock %}

{% block extra_css %}
<style>
    .ad-preview {
        border: 2px dashed #ddd;
        border-radius: 8px;
        padding: 20px;
        text-align: center;
        background: #f9f9f9;
        min-height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
    }
    
    .ad-preview img {
        max-width: 100%;
        max-height: 150px;
        border-radius: 4px;
        margin-bottom: 10px;
    }
    
    .position-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        border-radius: 12px;
    }
    
    .analytics-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
    }
    
    .analytics-number {
        font-size: 2rem;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }
    
    .ad-status-active { background-color: #28a745; }
    .ad-status-inactive { background-color: #6c757d; }
    .ad-status-scheduled { background-color: #ffc107; color: #000; }
    .ad-status-expired { background-color: #dc3545; }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-container">
    <!-- Header -->
    <div class="dashboard-header">
        <h1 class="dashboard-title">
            <i class="fas fa-bullhorn"></i>
            Advertisement Management
        </h1>
        <div class="header-actions">
            <button class="modern-btn modern-btn-primary" data-bs-toggle="modal" data-bs-target="#createAdModal">
                <i class="fas fa-plus"></i>
                <span>Create Advertisement</span>
            </button>
            <button class="modern-btn modern-btn-secondary" onclick="location.reload()">
                <i class="fas fa-sync-alt"></i>
                <span>Refresh</span>
            </button>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-lg-3 col-md-6">
            <div class="modern-stat-card stat-blue">
                <div class="stat-header">
                    <h6 class="stat-title">Total Ads</h6>
                    <div class="stat-icon">
                        <i class="fas fa-bullhorn"></i>
                    </div>
                </div>
                <div class="stat-value">{{ stats.total_ads or 0 }}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="modern-stat-card stat-green">
                <div class="stat-header">
                    <h6 class="stat-title">Active Ads</h6>
                    <div class="stat-icon">
                        <i class="fas fa-play-circle"></i>
                    </div>
                </div>
                <div class="stat-value">{{ stats.active_ads or 0 }}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="modern-stat-card stat-orange">
                <div class="stat-header">
                    <h6 class="stat-title">Scheduled</h6>
                    <div class="stat-icon">
                        <i class="fas fa-clock"></i>
                    </div>
                </div>
                <div class="stat-value">{{ stats.scheduled_ads or 0 }}</div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="modern-stat-card stat-red">
                <div class="stat-header">
                    <h6 class="stat-title">Inactive</h6>
                    <div class="stat-icon">
                        <i class="fas fa-pause-circle"></i>
                    </div>
                </div>
                <div class="stat-value">{{ stats.inactive_ads or 0 }}</div>
            </div>
        </div>
    </div>

    <!-- Advertisements Table -->
    <div class="modern-card">
        <div class="card-header">
            <h5 class="card-title">
                <i class="fas fa-list"></i>
                All Advertisements
            </h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="advertisementsTable">
                    <thead>
                        <tr>
                            <th>Preview</th>
                            <th>Title</th>
                            <th>Position</th>
                            <th>Type</th>
                            <th>Status</th>
                            <th>Priority</th>
                            <th>Analytics</th>
                            <th>Schedule</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for ad in advertisements %}
                        <tr>
                            <td>
                                <div class="ad-preview" style="width: 100px; height: 60px; min-height: auto;">
                                    {% if ad.image_url %}
                                        <img src="{{ ad.image_url }}" alt="Ad Preview" style="max-height: 50px;">
                                    {% else %}
                                        <i class="fas fa-image text-muted"></i>
                                    {% endif %}
                                </div>
                            </td>
                            <td>
                                <strong>{{ ad.title }}</strong>
                                {% if ad.description %}
                                    <br><small class="text-muted">{{ ad.description[:50] }}{% if ad.description|length > 50 %}...{% endif %}</small>
                                {% endif %}
                            </td>
                            <td>
                                <span class="position-badge badge bg-info">{{ ad.position.replace('_', ' ').title() }}</span>
                            </td>
                            <td>
                                <span class="badge bg-secondary">{{ ad.ad_type.title() }}</span>
                            </td>
                            <td>
                                <span class="badge ad-status-{{ ad.status }}">{{ ad.status.title() }}</span>
                            </td>
                            <td>
                                <span class="badge bg-primary">{{ ad.priority }}</span>
                            </td>
                            <td>
                                <small>
                                    <i class="fas fa-eye"></i> {{ ad.impressions or 0 }}<br>
                                    <i class="fas fa-mouse-pointer"></i> {{ ad.clicks or 0 }}
                                </small>
                            </td>
                            <td>
                                <small>
                                    {% if ad.start_date %}
                                        <strong>Start:</strong> {{ ad.start_date.strftime('%Y-%m-%d') }}<br>
                                    {% endif %}
                                    {% if ad.end_date %}
                                        <strong>End:</strong> {{ ad.end_date.strftime('%Y-%m-%d') }}
                                    {% endif %}
                                </small>
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <button class="btn btn-sm btn-outline-primary" onclick="editAdvertisement({{ ad.id }})" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger" onclick="deleteAdvertisement({{ ad.id }})" title="Delete">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- Create Advertisement Modal -->
<div class="modal fade" id="createAdModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Create New Advertisement</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createAdForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Title *</label>
                                <input type="text" class="form-control" name="title" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Description</label>
                                <textarea class="form-control" name="description" rows="3"></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Image URL</label>
                                <input type="url" class="form-control" name="image_url" placeholder="https://example.com/image.jpg">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Link URL</label>
                                <input type="url" class="form-control" name="link_url" placeholder="https://example.com">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Position *</label>
                                <select class="form-select" name="position" required>
                                    <option value="hero_banner">Hero Banner</option>
                                    <option value="sidebar">Sidebar</option>
                                    <option value="header">Header</option>
                                    <option value="footer">Footer</option>
                                    <option value="between_sections">Between Sections</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Ad Type *</label>
                                <select class="form-select" name="ad_type" required>
                                    <option value="banner">Banner</option>
                                    <option value="popup">Popup</option>
                                    <option value="inline">Inline</option>
                                    <option value="floating">Floating</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Priority</label>
                                <input type="number" class="form-control" name="priority" value="1" min="1" max="10">
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Status *</label>
                                <select class="form-select" name="status" required>
                                    <option value="active">Active</option>
                                    <option value="inactive">Inactive</option>
                                    <option value="scheduled">Scheduled</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Target Audience</label>
                                <select class="form-select" name="target_audience">
                                    <option value="all">All Users</option>
                                    <option value="patients">Patients Only</option>
                                    <option value="doctors">Doctors Only</option>
                                    <option value="new_users">New Users</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">Start Date</label>
                                <input type="datetime-local" class="form-control" name="start_date">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label class="form-label">End Date</label>
                                <input type="datetime-local" class="form-control" name="end_date">
                            </div>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" onclick="saveAdvertisement()">Create Advertisement</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Initialize DataTable
$(document).ready(function() {
    $('#advertisementsTable').DataTable({
        responsive: true,
        order: [[5, 'desc']], // Sort by priority
        columnDefs: [
            { orderable: false, targets: [0, 8] } // Disable sorting for preview and actions
        ]
    });
});

// Save Advertisement
function saveAdvertisement() {
    const form = document.getElementById('createAdForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData.entries());
    
    fetch('/admin/advertisements/create', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.message) {
            showAlert('success', data.message);
            $('#createAdModal').modal('hide');
            location.reload();
        } else {
            showAlert('danger', data.error || 'Failed to create advertisement');
        }
    })
    .catch(error => {
        showAlert('danger', 'Error creating advertisement');
        console.error('Error:', error);
    });
}

// Edit Advertisement
function editAdvertisement(adId) {
    // Implementation for editing advertisement
    showAlert('info', 'Edit functionality will be implemented');
}

// Delete Advertisement
function deleteAdvertisement(adId) {
    if (confirm('Are you sure you want to delete this advertisement?')) {
        fetch(`/admin/advertisements/delete/${adId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.message) {
                showAlert('success', data.message);
                location.reload();
            } else {
                showAlert('danger', data.error || 'Failed to delete advertisement');
            }
        })
        .catch(error => {
            showAlert('danger', 'Error deleting advertisement');
            console.error('Error:', error);
        });
    }
}

// Show Alert
function showAlert(type, message) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    document.querySelector('.dashboard-container').insertBefore(alertDiv, document.querySelector('.dashboard-container').firstChild);
    
    setTimeout(() => {
        alertDiv.remove();
    }, 5000);
}
</script>
{% endblock %}
