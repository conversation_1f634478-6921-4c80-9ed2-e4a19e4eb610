#!/usr/bin/env python3
"""
Simple test script to verify the application starts without errors
"""

import sys
import os

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_app_startup():
    """Test if the Flask app can be imported and created without errors"""
    try:
        print("Testing application startup...")
        
        # Try to import the main app
        from app import app
        print("✅ App imported successfully")
        
        # Test if app is a Flask instance
        from flask import Flask
        if isinstance(app, Flask):
            print("✅ App is a valid Flask instance")
        else:
            print("❌ App is not a Flask instance")
            return False
        
        # Test if app has required configurations
        required_configs = ['SECRET_KEY']
        for config in required_configs:
            if config in app.config and app.config[config]:
                print(f"✅ {config} is configured")
            else:
                print(f"⚠️  {config} is not configured or empty")
        
        # Test if blueprints are registered
        blueprint_names = [bp.name for bp in app.blueprints.values()]
        expected_blueprints = ['admin', 'doctor', 'staff']
        
        for bp_name in expected_blueprints:
            if bp_name in blueprint_names:
                print(f"✅ {bp_name} blueprint registered")
            else:
                print(f"❌ {bp_name} blueprint not found")
        
        print("\n🎉 Application startup test completed successfully!")
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_advertisement_routes():
    """Test if advertisement routes are properly registered"""
    try:
        from app import app

        # Check if advertisement routes exist in the main app
        ad_routes = []
        with app.app_context():
            for rule in app.url_map.iter_rules():
                if 'advertisement' in rule.rule:
                    ad_routes.append(rule.rule)

        expected_routes = [
            '/admin/advertisements',
            '/admin/advertisements/create',
            '/admin/advertisements/upload-image'
        ]

        print("\nTesting advertisement routes...")
        for route in expected_routes:
            if any(route in r for r in ad_routes):
                print(f"✅ Route {route} is registered")
            else:
                print(f"❌ Route {route} not found")

        # Also check if the view functions exist
        print("\nTesting advertisement view functions...")
        view_functions = [
            'admin.admin_advertisements',
            'admin.create_advertisement',
            'admin.upload_advertisement_image'
        ]

        for view_func in view_functions:
            try:
                endpoint = app.view_functions.get(view_func)
                if endpoint:
                    print(f"✅ View function {view_func} exists")
                else:
                    print(f"❌ View function {view_func} not found")
            except:
                print(f"⚠️  Could not check view function {view_func}")

        return True

    except Exception as e:
        print(f"❌ Error testing advertisement routes: {e}")
        return False

if __name__ == "__main__":
    print("=" * 50)
    print("CVBIOLABS Application Startup Test")
    print("=" * 50)
    
    # Test basic app startup
    startup_success = test_app_startup()
    
    # Test advertisement routes
    routes_success = test_advertisement_routes()
    
    print("\n" + "=" * 50)
    if startup_success and routes_success:
        print("🎉 All tests passed! Application is ready to run.")
        print("\nTo start the application:")
        print("python app.py")
        print("\nTo access advertisement management:")
        print("http://localhost:5000/admin/advertisements")
    else:
        print("❌ Some tests failed. Please check the errors above.")
    print("=" * 50)
