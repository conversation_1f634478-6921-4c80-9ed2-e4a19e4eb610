from flask import Flask, render_template, request, redirect, url_for, flash, session, jsonify, get_flashed_messages, send_file, send_from_directory, make_response
from flask_session import Session
from flask_mail import Mail, Message, Connection
from werkzeug.security import generate_password_hash, check_password_hash
from flask_wtf.csrf import CSRFProtect, generate_csrf
from flask_login import LoginManager, UserMixin, login_user, login_required, logout_user, current_user
import mysql.connector
from mysql.connector import Error
from datetime import datetime, date, time, timedelta
import os
import uuid
import pyotp
from dotenv import load_dotenv
import razorpay
import json
import requests
from flask_cors import CORS
from doctor import doctor_bp, Doctor, get_db_connection
from staff import staff_bp
from flask_login import login_required as staff_login_required
from functools import wraps
import redis
from admin import admin_bp, DecimalEncoder
import secrets
import random
import bcrypt
import logging
from flask_limiter import Limiter
# Removed unused import - using custom get_client_ip function instead
import smtplib
from security_utils import secure_auth, secure_otp, input_validator, secure_signature
from api_security import api_auth, jwt_required, role_required, validate_json_request, api_error_handler
from error_handlers import register_error_handlers, security_logger
from security_monitoring import security_monitor, audit_logger, security_metrics

# Suppress smtplib debug output by configuring its logger
logging.getLogger('smtplib').setLevel(logging.CRITICAL)
logging.getLogger('flask_mail').setLevel(logging.CRITICAL)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

load_dotenv()

app = Flask(__name__)

# Secure session configuration
app.config['SESSION_COOKIE_SECURE'] = os.getenv('FLASK_ENV') != 'development'  # Secure unless explicitly in development
app.config['SESSION_COOKIE_HTTPONLY'] = True
app.config['SESSION_COOKIE_SAMESITE'] = 'Lax'
app.config['PERMANENT_SESSION_LIFETIME'] = timedelta(hours=2)  # Shorter session lifetime

# Require SECRET_KEY from environment in production
if os.getenv('FLASK_ENV', 'production') == 'production':
    if not os.getenv('SECRET_KEY'):
        raise RuntimeError('SECRET_KEY must be set in production!')
    app.secret_key = os.getenv('SECRET_KEY')
else:
    app.secret_key = os.urandom(24)

# Configure CORS origins securely
cors_origins = os.getenv('CORS_ORIGINS')
if cors_origins:
    # Parse allowed origins from environment
    allowed_origins = [origin.strip() for origin in cors_origins.split(',')]
else:
    # Default to localhost for development, require explicit configuration for production
    if os.getenv('FLASK_ENV') == 'production':
        raise RuntimeError('CORS_ORIGINS must be explicitly set in production!')
    allowed_origins = [
        'http://localhost:5000',
        'http://127.0.0.1:5000',
        'http://localhost:3000',  # For frontend development
        'http://127.0.0.1:3000'
    ]

CORS(app,
     supports_credentials=True,
     origins=allowed_origins,
     methods=['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
     allow_headers=['Content-Type', 'Authorization', 'X-Requested-With', 'X-CSRF-Token'])

# Add Flask-Limiter for rate limiting with better configuration
def get_client_ip():
    """Get client IP with proper proxy handling"""
    # Check for X-Forwarded-For header (common with reverse proxies)
    if request.headers.get('X-Forwarded-For'):
        return request.headers.get('X-Forwarded-For').split(',')[0].strip()
    # Check for X-Real-IP header (nginx)
    elif request.headers.get('X-Real-IP'):
        return request.headers.get('X-Real-IP')
    # Fallback to remote_addr
    else:
        return request.remote_addr

# Configure rate limiter with Redis for production, memory for development
redis_url = os.getenv('REDIS_URL')
if redis_url and os.getenv('FLASK_ENV') == 'production':
    storage_uri = redis_url
else:
    storage_uri = "memory://"

limiter = Limiter(
    get_client_ip,
    app=app,
    default_limits=["200 per day", "50 per hour"],
    storage_uri=storage_uri,
    headers_enabled=True,  # Include rate limit headers in response
    swallow_errors=True    # Don't crash on rate limiter errors
)

# Configure app (session settings moved above)
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Configure CSRF protection
app.config['WTF_CSRF_CHECK_DEFAULT'] = True  # Enable CSRF check by default
app.config['WTF_CSRF_ENABLED'] = True  # Enable CSRF protection
app.config['WTF_CSRF_TIME_LIMIT'] = 3600  # 1 hour CSRF token validity
# Only require HTTPS for CSRF in production, allow HTTP in development
app.config['WTF_CSRF_SSL_STRICT'] = os.getenv('FLASK_ENV') == 'production'

# Initialize CSRF protection
csrf = CSRFProtect(app)

# Make CSRF token available in all templates
@app.context_processor
def inject_csrf_token():
    return dict(csrf_token=generate_csrf())

# Initialize API authentication
api_auth.init_app(app)

# Register secure error handlers
register_error_handlers(app)

# Initialize Flask-Login
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'Please log in to access this page.'
login_manager.login_message_category = 'error'

@login_manager.user_loader
def load_user(user_id):
    role = session.get('user_role')
    conn = get_db_connection()
    try:
        with conn.cursor(dictionary=True) as cursor:
            if role == 'doctor':
                cursor.execute("SELECT * FROM doctors WHERE id = %s", (user_id,))
                doctor = cursor.fetchone()
                if doctor:
                    return User({
                        'id': doctor['id'],
                        'email': doctor['email'],
                        'username': doctor['name'],
                        'role': 'doctor'
                    })
            elif role == 'staff':
                cursor.execute("SELECT * FROM admin_users WHERE id = %s", (user_id,))
                staff = cursor.fetchone()
                if staff:
                    return User({
                        'id': staff['id'],
                        'email': staff['email'],
                        'username': staff['name'],
                        'role': staff['role']
                    })
            else:
                cursor.execute("SELECT * FROM users WHERE id = %s", (user_id,))
                user = cursor.fetchone()
                if user:
                    return User(user)
            return None
    finally:
        conn.close()

# Register blueprints
app.register_blueprint(doctor_bp, url_prefix='/doctor')
app.register_blueprint(staff_bp, url_prefix='/staff')
app.register_blueprint(admin_bp)

# Configure CSRF exemptions for admin routes
@csrf.exempt
def exempt_admin_routes():
    """Exempt specific admin routes from CSRF protection"""
    pass

# Apply CSRF exemption to advertisement routes
csrf.exempt(admin_bp.view_functions.get('create_advertisement'))
csrf.exempt(admin_bp.view_functions.get('update_advertisement'))
csrf.exempt(admin_bp.view_functions.get('delete_advertisement'))
csrf.exempt(admin_bp.view_functions.get('upload_advertisement_image'))

# Set JSON encoder
app.json_encoder = DecimalEncoder

# Configure Flask-Session with Redis for production, filesystem for development
redis_url = os.getenv('REDIS_URL')
if redis_url and os.getenv('FLASK_ENV') == 'production':
    app.config['SESSION_TYPE'] = 'redis'
    app.config['SESSION_REDIS'] = redis.from_url(redis_url)
    app.config['SESSION_USE_SIGNER'] = True
    app.config['SESSION_KEY_PREFIX'] = 'cvbiolabs:'
else:
    # Use filesystem sessions for development
    app.config['SESSION_TYPE'] = 'filesystem'
    app.config['SESSION_FILE_DIR'] = './flask_session'
    app.config['SESSION_FILE_THRESHOLD'] = 100

Session(app)

# MySQL Configuration
app.config['MYSQL_HOST'] = os.getenv('DB_HOST')
app.config['MYSQL_USER'] = os.getenv('DB_USER')
app.config['MYSQL_PASSWORD'] = os.getenv('DB_PASSWORD')
app.config['MYSQL_DB'] = os.getenv('DB_NAME')
app.config['MYSQL_CHARSET'] = os.getenv('DB_CHARSET', 'utf8mb4')

# Mail Configuration
app.config['MAIL_SERVER'] = os.getenv('MAIL_SERVER', 'smtpout.secureserver.net')
app.config['MAIL_PORT'] = int(os.getenv('MAIL_PORT', 465))
app.config['MAIL_USE_TLS'] = os.getenv('MAIL_USE_TLS', 'False').lower() == 'true'
app.config['MAIL_USE_SSL'] = os.getenv('MAIL_USE_SSL', 'True').lower() == 'true'
app.config['MAIL_USERNAME'] = os.getenv('MAIL_USERNAME')
app.config['MAIL_PASSWORD'] = os.getenv('MAIL_PASSWORD')
app.config['MAIL_DEFAULT_SENDER'] = os.getenv('MAIL_DEFAULT_SENDER', os.getenv('MAIL_USERNAME'))
app.config['MAIL_DEBUG'] = False
app.config['MAIL_SUPPRESS_SEND'] = os.getenv('MAIL_SUPPRESS_SEND', 'False').lower() == 'true'

if not app.config['MAIL_USERNAME'] or not app.config['MAIL_PASSWORD']:
    logger.warning("Email credentials not set in environment variables!")

# Patch Flask-Mail Connection to ensure no debug output
def no_debug_connection(self, mail):
    logger.info("Applying no_debug_connection patch")
    self.host = smtplib.SMTP(
        mail.server, mail.port, timeout=mail.timeout
    )
    self.host.set_debuglevel(0)  # Explicitly set debug level to 0
    if mail.use_tls:
        self.host.starttls()
    if mail.username and mail.password:
        self.host.login(mail.username, mail.password)

Connection._init_ = no_debug_connection

# Initialize Flask-Mail
mail = Mail(app)

# Initialize professional email service (will be done within app context)
from email_service import init_email_service
email_service = None

# Initialize email service within app context
with app.app_context():
    email_service = init_email_service(mail)


from flask_mail import Connection

def no_debuglevel(self):
    self.host.set_debuglevel(0)
Connection._set_debuglevel = no_debuglevel


_original_smtp_init = smtplib.SMTP.__init__

def smtp_init_patch(self, *args, **kwargs):
    _original_smtp_init(self, *args, **kwargs)
    self.set_debuglevel(0)

smtplib.SMTP.__init__ = smtp_init_patch




# Initialize Razorpay client
razorpay_client = razorpay.Client(auth=(os.getenv('RAZORPAY_KEY_ID'), os.getenv('RAZORPAY_KEY_SECRET')))

# Add reCAPTCHA configuration from environment
app.config['RECAPTCHA_SITE_KEY'] = os.getenv('RECAPTCHA_SITE_KEY')
app.config['RECAPTCHA_SECRET_KEY'] = os.getenv('RECAPTCHA_SECRET_KEY')

# User class for Flask-Login
class User(UserMixin):
    def __init__(self, user_data):
        self.id = user_data['id']
        self.email = user_data['email']
        self.username = user_data.get('username', user_data.get('name', ''))
        self.role = user_data.get('role', 'patient')

    def is_staff(self):
        return self.role in ['Admin', 'Doctor', 'Receptionist']

# Database connection
def get_db_connection():
    try:
        return mysql.connector.connect(
            host=app.config['MYSQL_HOST'],
            user=app.config['MYSQL_USER'],
            password=app.config['MYSQL_PASSWORD'],
            database=app.config['MYSQL_DB'],
            charset=app.config['MYSQL_CHARSET']
        )
    except Error as e:
        logger.error(f"Error connecting to MySQL: {e}", exc_info=True)
        raise

# Session security utilities
def regenerate_session():
    """Regenerate session ID to prevent session fixation attacks"""
    try:
        # Store current session data
        session_data = dict(session)
        # Clear the session to force new session ID
        session.clear()
        # Restore session data
        session.update(session_data)
        session.permanent = True
        # Mark session as regenerated
        session['_session_regenerated'] = True
        logger.info("Session regenerated successfully")
    except Exception as e:
        logger.error(f"Session regeneration failed: {e}")
        # Continue without regeneration rather than failing

# Secure admin credential management
class AdminManager:
    """Secure admin credential management"""

    @staticmethod
    def get_admin_credentials():
        """Get admin credentials from environment with validation"""
        admin_username = os.getenv('ADMIN_USERNAME')
        admin_password = os.getenv('ADMIN_PASSWORD')

        if not admin_username or not admin_password:
            raise RuntimeError('ADMIN_USERNAME and ADMIN_PASSWORD must be set in environment!')

        # Validate admin email format
        if not input_validator.validate_email(admin_username):
            raise RuntimeError('ADMIN_USERNAME must be a valid email address!')

        return admin_username, admin_password

    @staticmethod
    def verify_admin_credentials(email, password):
        """Verify admin credentials securely"""
        try:
            admin_username, admin_password = AdminManager.get_admin_credentials()

            # Use constant-time comparison to prevent timing attacks
            username_match = secrets.compare_digest(email, admin_username)
            password_match = secrets.compare_digest(password, admin_password)

            return username_match and password_match
        except Exception as e:
            logger.error(f"Admin credential verification failed: {e}")
            return False

def send_otp_email(email, otp, user_name=None):
    try:
        # Validate email before sending
        if not input_validator.validate_email(email):
            logger.error(f"Invalid email format: {email}")
            return False

        # Use the new professional email service
        from email_service import get_email_service
        service = get_email_service()
        return service.send_otp_verification(
            recipient=email,
            user_name=user_name or "Valued Customer",
            otp=otp
        )
    except Exception as e:
        logger.error(f"Error sending email: {e}", exc_info=True)
        # Fallback to simple email if professional service fails
        try:
            msg = Message('CVBioLabs - Email Verification OTP',
                        sender=app.config['MAIL_USERNAME'],
                        recipients=[email])
            msg.body = f"Your OTP for CVBioLabs verification is: {otp}"
            mail.send(msg)
            return True
        except Exception as fallback_error:
            logger.error(f"Fallback email also failed: {fallback_error}", exc_info=True)
            return False

def verify_recaptcha(token):
    # Skip reCAPTCHA verification in development mode
    if os.getenv('FLASK_ENV') == 'development':
        logger.info("Skipping reCAPTCHA verification in development mode")
        return True

    # Handle development/fallback tokens
    if token and (token.startswith('development') or token.startswith('test')):
        logger.info("Using development reCAPTCHA token")
        return True

    # Use the correct secret key from config
    secret = app.config['RECAPTCHA_SECRET_KEY']
    if not secret:
        logger.error("reCAPTCHA secret key not configured")
        return True  # Allow in development if not configured

    # Handle Google's test keys
    if secret == '6LeIxAcTAAAAAGG-vFI1TnRWxMZNFuojJ4WifJWe':
        logger.info("Using Google test reCAPTCHA key - allowing request")
        return True

    try:
        response = requests.post(
            'https://www.google.com/recaptcha/api/siteverify',
            data={'secret': secret, 'response': token},
            timeout=10
        )
        result = response.json()
        logger.info(f"reCAPTCHA verification result: {result}")

        # For v3, check score; for v2, just check success
        if result.get('success', False):
            score = result.get('score', 1.0)  # Default to 1.0 for v2
            return score >= 0.3  # Lower threshold for better UX
        return False

    except requests.exceptions.Timeout:
        logger.warning("reCAPTCHA verification timeout - allowing request")
        return True  # Allow on timeout to prevent blocking users
    except Exception as e:
        logger.error(f"reCAPTCHA verification error: {str(e)}")
        return True  # Allow on error in development

# Custom filter to calculate age
@app.template_filter('calculate_age')
def calculate_age(dob):
    if not dob:
        return ''
    today = datetime.today()
    age = today.year - dob.year
    if today.month < dob.month or (today.month == dob.month and today.day < dob.day):
        age -= 1
    return age

# Advertisement helper function
def get_active_advertisements(position=None, user_type='guest'):
    """Get active advertisements for a specific position and user type"""
    try:
        conn = get_db_connection()
        if not conn:
            return []

        cursor = conn.cursor(dictionary=True)

        # Build query based on parameters
        query = """
            SELECT * FROM advertisements
            WHERE status = 'active'
            AND (start_date IS NULL OR start_date <= NOW())
            AND (end_date IS NULL OR end_date >= NOW())
            AND (target_audience = 'all' OR target_audience = %s)
        """
        params = [user_type]

        if position:
            query += " AND position = %s"
            params.append(position)

        query += " ORDER BY priority DESC, created_at DESC"

        cursor.execute(query, params)
        ads = cursor.fetchall()

        # Track impressions for each ad
        for ad in ads:
            cursor.execute("""
                INSERT INTO advertisement_analytics (ad_id, event_type, user_type, ip_address, timestamp)
                VALUES (%s, 'impression', %s, %s, NOW())
            """, (ad['id'], user_type, request.remote_addr))

            # Update impression count
            cursor.execute("""
                UPDATE advertisements SET impression_count = impression_count + 1 WHERE id = %s
            """, (ad['id'],))

        conn.commit()
        conn.close()
        return ads

    except Exception as e:
        logger.error(f"Error fetching advertisements: {str(e)}")
        return []

@app.route('/')
def home():
    # Determine user type for targeted ads
    user_type = 'guest'
    if current_user.is_authenticated:
        user_type = 'patients' if current_user.role == 'patient' else current_user.role.lower()

    # Get advertisements for different positions
    hero_ads = get_active_advertisements('hero_banner', user_type)
    sidebar_ads = get_active_advertisements('sidebar', user_type)
    header_ads = get_active_advertisements('header', user_type)
    footer_ads = get_active_advertisements('footer', user_type)
    section_ads = get_active_advertisements('between_sections', user_type)

    if current_user.is_authenticated:
        return render_template('home.html',
                             is_authenticated=True,
                             username=current_user.username,
                             hero_ads=hero_ads,
                             sidebar_ads=sidebar_ads,
                             header_ads=header_ads,
                             footer_ads=footer_ads,
                             section_ads=section_ads)
    return render_template('home.html',
                         is_authenticated=False,
                         username=None,
                         hero_ads=hero_ads,
                         sidebar_ads=sidebar_ads,
                         header_ads=header_ads,
                         footer_ads=footer_ads,
                         section_ads=section_ads)

# Advertisement click tracking
@app.route('/ad/click/<int:ad_id>')
def track_ad_click(ad_id):
    """Track advertisement clicks and redirect to the target URL"""
    try:
        conn = get_db_connection()
        if conn:
            cursor = conn.cursor(dictionary=True)

            # Get advertisement details
            cursor.execute("SELECT link_url FROM advertisements WHERE id = %s AND status = 'active'", (ad_id,))
            ad = cursor.fetchone()

            if ad and ad['link_url']:
                # Track the click
                user_type = 'guest'
                if current_user.is_authenticated:
                    user_type = 'patients' if current_user.role == 'patient' else current_user.role.lower()

                cursor.execute("""
                    INSERT INTO advertisement_analytics (ad_id, event_type, user_type, ip_address, timestamp)
                    VALUES (%s, 'click', %s, %s, NOW())
                """, (ad_id, user_type, request.remote_addr))

                # Update click count
                cursor.execute("""
                    UPDATE advertisements SET click_count = click_count + 1 WHERE id = %s
                """, (ad_id,))

                conn.commit()
                conn.close()

                # Redirect to the advertisement URL
                return redirect(ad['link_url'])

            conn.close()

    except Exception as e:
        logger.error(f"Error tracking ad click: {str(e)}")

    # If something goes wrong, redirect to home
    return redirect(url_for('home'))

@app.route('/login', methods=['GET', 'POST'])
@limiter.limit("10 per minute")  # More reasonable rate limiting for login attempts
def login():
    # Clear any existing user session data but preserve CSRF token
    if current_user.is_authenticated:
        logout_user()

    # Clear user-specific session data but preserve CSRF token
    user_session_keys = ['user_id', 'email', 'user_role', 'doctor_id', 'doctor_name',
                        'agent_id', 'agent_name', 'temp_user_id']
    for key in user_session_keys:
        session.pop(key, None)

    if request.method == 'POST':
        try:
            recaptcha_token = request.form.get('recaptcha_token')
            if not recaptcha_token or not verify_recaptcha(recaptcha_token):
                return jsonify({
                    'status': 'error',
                    'message': 'Security check failed'
                }), 400

            email = request.form.get('email', '').strip().lower()
            password = request.form.get('password', '')
            next_page = request.form.get('next') or request.args.get('next')

            # Validate inputs
            if not email or not password:
                return jsonify({
                    'status': 'error',
                    'message': 'Email and password are required'
                }), 400

            # Validate email format
            if not input_validator.validate_email(email):
                return jsonify({
                    'status': 'error',
                    'message': 'Please enter a valid email address'
                }), 400

            # Verify database connection and tables exist
            conn = get_db_connection()
            if not conn:
                return jsonify({
                    'status': 'error',
                    'message': 'Database connection failed'
                }), 500

            try:
                with conn.cursor(dictionary=True) as cur:
                    # Verify required tables exist
                    cur.execute("SHOW TABLES LIKE 'users'")
                    if not cur.fetchone():
                        return jsonify({
                            'status': 'error',
                            'message': 'Database not properly initialized'
                        }), 500

                    # --- ADMIN LOGIN CHECK ---
                    if AdminManager.verify_admin_credentials(email, password):
                        admin_user = User({
                            'id': 0,
                            'email': email,
                            'username': 'Admin',
                            'role': 'admin'
                        })
                        login_user(admin_user)
                        # Regenerate session to prevent session fixation
                        regenerate_session()
                        session['user_id'] = 0
                        session['user_name'] = 'Admin'
                        session['user_role'] = 'admin'

                        # Log successful admin login
                        audit_logger.log_authentication_event('LOGIN', email, success=True)
                        security_metrics.increment_metric('successful_logins')
                        security_metrics.increment_metric('admin_logins')

                        # Generate JWT tokens for API access
                        from api_security import APIAuth
                        access_token, refresh_token = APIAuth.generate_tokens(0, 'admin')

                        response_data = {
                            'status': 'success',
                            'message': 'Admin login successful',
                            'redirect': '/admin/dashboard'
                        }

                        # Add tokens for API access (optional)
                        if access_token:
                            response_data['access_token'] = access_token
                            response_data['refresh_token'] = refresh_token

                        return jsonify(response_data)

                    # Try doctor login first
                    cur.execute("SELECT * FROM doctors WHERE email = %s AND status = 'active'", (email,))
                    doctor = cur.fetchone()
                    
                    if doctor and doctor['password_hash']:
                        try:
                            if secure_auth.verify_password(password, doctor['password_hash']):
                                user = User({
                                    'id': doctor['id'],
                                    'email': doctor['email'],
                                    'username': doctor['name'],
                                    'role': 'doctor'
                                })
                                login_user(user)
                                regenerate_session()
                                session['doctor_id'] = doctor['id']
                                session['doctor_name'] = doctor['name']
                                session['user_role'] = 'doctor'

                                # Log successful doctor login
                                audit_logger.log_authentication_event('LOGIN', email, success=True)
                                security_metrics.increment_metric('successful_logins')
                                security_metrics.increment_metric('doctor_logins')
                                return jsonify({
                                    'status': 'success',
                                    'message': 'Doctor login successful',
                                    'redirect': url_for('doctor.doctor_dashboard')
                                })
                        except Exception as e:
                            logger.error(f"Doctor password verification error: {str(e)}", exc_info=True)
                            return jsonify({
                                'status': 'error',
                                'message': 'Invalid doctor credentials'
                            }, 401)

                    # Try staff login
                    cur.execute("""
                        SELECT * FROM admin_users 
                        WHERE email = %s AND role = 'Receptionist' AND status = 'active'
                    """, (email,))
                    staff = cur.fetchone()
                    
                    if staff and staff['password_hash']:
                        try:
                            if secure_auth.verify_password(password, staff['password_hash']):
                                user = User({
                                    'id': staff['id'],
                                    'email': staff['email'],
                                    'username': staff['name'],
                                    'role': staff['role']
                                })
                                login_user(user)
                                regenerate_session()
                                session['user_id'] = staff['id']
                                session['user_name'] = staff['name']
                                session['user_role'] = 'staff'
                                
                                if next_page and next_page.startswith('/staff/'):
                                    return jsonify({
                                        'status': 'success',
                                        'message': 'Staff login successful',
                                        'redirect': url_for('staff.dashboard')
                                    })
                                
                                return jsonify({
                                    'status': 'success',
                                    'message': 'Staff login successful',
                                    'redirect': url_for('staff.dashboard')
                                })
                        except Exception as e:
                            logger.error(f"Staff password verification error: {str(e)}", exc_info=True)
                            return jsonify({
                                'status': 'error',
                                'message': 'Invalid staff credentials'
                            }), 401
                    
                    # Try pickup agent login
                    cur.execute("SELECT * FROM pickup_agents WHERE email = %s", (email,))
                    agent = cur.fetchone()
                    
                    if agent and agent['password_hash']:
                        try:
                            if secure_auth.verify_password(password, agent['password_hash']):
                                regenerate_session()
                                session['agent_id'] = agent['id']
                                session['agent_name'] = agent['name']
                                return jsonify({
                                    'status': 'success',
                                    'message': 'Login successful',
                                    'redirect': url_for('pickup_agent', section='dashboard', agent_id=agent['id'])
                                })
                        except Exception as e:
                            logger.error(f"Agent password verification error: {str(e)}", exc_info=True)
                    # Try regular user login (using Werkzeug's check_password_hash)
                    cur.execute("SELECT * FROM users WHERE email = %s", (email,))
                    user = cur.fetchone()
                    
                    if user and user['password_hash']:
                        try:
                            if check_password_hash(user['password_hash'], password):
                                if not user['otp_verified']:
                                    session['temp_user_id'] = user['id']
                                    return jsonify({
                                        'status': 'error',
                                        'message': 'Please verify your email first',
                                        'redirect': url_for('verify_otp')
                                    })
                                user_obj = User(user)
                                login_user(user_obj)
                                regenerate_session()
                                session['user_id'] = user['id']
                                session['email'] = user['email']
                                session['user_role'] = 'patient'
                                
                                return jsonify({
                                    'status': 'success',
                                    'message': 'Login successful',
                                    'redirect': url_for('home')
                                })
                        except Exception as e:
                            logger.error(f"User password verification error: {str(e)}", exc_info=True)
                    
                    # If no login succeeds, log and return generic error
                    security_logger.log_failed_login(email, 'Invalid credentials - no matching account found')
                    security_monitor.log_failed_login(request.remote_addr, email, 'Invalid credentials')
                    security_metrics.increment_metric('failed_logins')
                    return jsonify({
                        'status': 'error',
                        'message': 'Invalid email or password'
                    }), 401
                    
            except Error as e:
                logger.error(f"Database error: {str(e)}", exc_info=True)
                return jsonify({
                    'status': 'error',
                    'message': 'Database error occurred'
                }), 500
                
        except Exception as e:
            logger.error(f"Login error: {str(e)}", exc_info=True)
            return jsonify({
                'status': 'error',
                'message': 'Login failed. Please try again.'
            }), 500
        finally:
            if 'conn' in locals():
                conn.close()

    return render_template('login_signup.html', 
                         recaptcha_site_key=app.config['RECAPTCHA_SITE_KEY'],
                         next=request.args.get('next'))

@app.route('/signup', methods=['GET', 'POST'])
@limiter.limit("5 per minute")  # More reasonable rate limiting for signup attempts
def signup():
    if request.method == 'POST':
        try:
            recaptcha_token = request.form.get('recaptcha_token')
            if not recaptcha_token or not verify_recaptcha(recaptcha_token):
                return jsonify({
                    'status': 'error',
                    'message': 'Security check failed'
                }), 400

            # Get form data
            firstname = request.form.get('firstname')
            lastname = request.form.get('lastname')
            name = f"{firstname} {lastname}".strip()
            email = request.form.get('email')
            phone = request.form.get('phone')
            password = request.form.get('password')
            
            # Validate required fields
            if not all([firstname, lastname, email, phone, password]):
                return jsonify({
                    'status': 'error',
                    'message': 'All fields are required'
                }), 400

            # Validate email format
            if not input_validator.validate_email(email):
                return jsonify({
                    'status': 'error',
                    'message': 'Please enter a valid email address'
                }), 400

            # Validate phone format
            if not input_validator.validate_phone(phone):
                return jsonify({
                    'status': 'error',
                    'message': 'Please enter a valid phone number'
                }), 400

            # Sanitize inputs
            firstname = input_validator.sanitize_input(firstname)
            lastname = input_validator.sanitize_input(lastname)
            name = f"{firstname} {lastname}".strip()

            # Check for existing credentials across all tables
            conn = get_db_connection()
            with conn.cursor(dictionary=True) as cur:  # Changed to dictionary cursor
                # Check email in all tables
                cur.execute("""
                    SELECT 'users' as table_name, id FROM users WHERE email = %s
                    UNION ALL
                    SELECT 'admin_users' as table_name, id FROM admin_users WHERE email = %s
                    UNION ALL
                    SELECT 'doctors' as table_name, id FROM doctors WHERE email = %s
                    UNION ALL
                    SELECT 'pickup_agents' as table_name, id FROM pickup_agents WHERE email = %s
                """, (email, email, email, email))
                existing_email = cur.fetchone()
                
                if existing_email:
                    return jsonify({
                        'status': 'error',
                        'message': f'Email already registered as {existing_email["table_name"].replace("_", " ").title()}'
                    }), 400

                # Check phone number in all relevant tables
                cur.execute("""
                    SELECT 'user_profiles' as table_name, user_id FROM user_profiles WHERE phone = %s
                    UNION ALL
                    SELECT 'admin_users' as table_name, id FROM admin_users WHERE phone = %s
                    UNION ALL
                    SELECT 'doctors' as table_name, id FROM doctors WHERE phone = %s
                    UNION ALL
                    SELECT 'pickup_agents' as table_name, id FROM pickup_agents WHERE phone = %s
                """, (phone, phone, phone, phone))
                existing_phone = cur.fetchone()
                
                if existing_phone:
                    return jsonify({
                        'status': 'error',
                        'message': f'Phone number already registered as {existing_phone["table_name"].replace("_", " ").title()}'
                    }), 400

                # Validate password strength
                is_valid, error_msg = input_validator.validate_password_strength(password)
                if not is_valid:
                    return jsonify({
                        'status': 'error',
                        'message': error_msg
                    }), 400

                # If no existing credentials found, proceed with registration
                password_hash = secure_auth.hash_password(password)
                otp = secure_auth.generate_otp()
                
                # Store signup data in session
                session['signup_data'] = {
                    'name': name,
                    'username': name,
                    'email': email,
                    'phone': phone,
                    'password_hash': password_hash,
                    'otp': otp
                }

                # Try to send OTP
                if send_otp_email(email, otp):
                    return jsonify({
                        'status': 'success',
                        'message': 'Please verify your email',
                        'redirect': url_for('verify_otp')
                    })
                else:
                    return jsonify({
                        'status': 'error',
                        'message': 'Failed to send verification email'
                    }), 500

        except Exception as e:
            logger.error(f"Signup error: {str(e)}", exc_info=True)
            return jsonify({
                'status': 'error',
                'message': 'Registration failed. Please try again.'
            }), 500
        finally:
            if 'conn' in locals():
                conn.close()

    return render_template('login_signup.html', recaptcha_site_key=app.config['RECAPTCHA_SITE_KEY'])

# Fix verify_otp route first
@app.route('/verify-otp', methods=['GET', 'POST'])
def verify_otp():
    if 'signup_data' not in session:
        flash('Please complete signup first', 'error')
        return redirect(url_for('signup'))

    if request.method == 'POST':
        try:
            signup_data = session.get('signup_data')
            submitted_otp = request.form.get('otp')
            stored_otp = signup_data.get('otp')

            if submitted_otp == stored_otp:
                conn = get_db_connection()
                with conn.cursor() as cursor:
                    # Create the user account with all required fields
                    cursor.execute("""
                        INSERT INTO users 
                        (username, email, password_hash, otp_verified) 
                        VALUES (%s, %s, %s, %s)
                    """, (
                        signup_data['username'],
                        signup_data['email'],
                        signup_data['password_hash'],
                        True
                    ))
                    conn.commit()
                    user_id = cursor.lastrowid

                    # Also create user profile
                    cursor.execute("""
                        INSERT INTO user_profiles 
                        (user_id, first_name, last_name, phone) 
                        VALUES (%s, %s, %s, %s)
                    """, (
                        user_id,
                        signup_data['name'].split()[0],
                        ' '.join(signup_data['name'].split()[1:]) if len(signup_data['name'].split()) > 1 else '',
                        signup_data['phone']
                    ))
                    conn.commit()

                    # Create User object and login with Flask-Login
                    user_data = {
                        'id': user_id,
                        'email': signup_data['email'],
                        'username': signup_data['username']
                    }
                    user = User(user_data)
                    login_user(user)

                    # Clear signup data and set session
                    session.pop('signup_data', None)
                    session['user_id'] = user_id
                    session['email'] = signup_data['email']

                flash('Email verified successfully!', 'success')
                return redirect(url_for('home'))

            else:
                flash('Invalid OTP', 'error')
                return redirect(url_for('verify_otp'))

        except Exception as e:
            logger.error(f"OTP verification error: {str(e)}", exc_info=True)
            flash('Verification failed. Please try again.', 'error')
            return redirect(url_for('verify_otp'))
        finally:
            if 'conn' in locals():
                conn.close()

    return render_template('verify_otp.html')

@app.route('/forgot_password', methods=['GET', 'POST'])
@limiter.limit("8 per minute")  # More reasonable rate limit for password reset attempts
def forgot_password():
    if request.method == 'POST':
        email = request.form['email']
        
        try:
            connection = get_db_connection()
            with connection.cursor(dictionary=True) as cursor:
                # Check if email exists and get user details
                sql = "SELECT u.*, up.first_name, up.last_name FROM users u LEFT JOIN user_profiles up ON u.id = up.user_id WHERE u.email = %s"
                cursor.execute(sql, (email,))
                user = cursor.fetchone()

                if not user:
                    flash('User does not exist. Please check your email or sign up.', 'error')
                    return redirect(url_for('forgot_password'))

                # Generate secure OTP
                otp = secure_otp.generate_totp()

                # Get user's full name
                if user.get('first_name') and user.get('last_name'):
                    user_name = f"{user['first_name']} {user['last_name']}"
                elif user.get('username'):
                    user_name = user['username']
                else:
                    user_name = 'Valued Customer'

                # Send OTP via email using professional service
                try:
                    from email_service import get_email_service
                    service = get_email_service()

                    success = service.send_password_reset(
                        recipient=email,
                        user_name=user_name,
                        otp=otp
                    )

                    if not success:
                        # Fallback to simple email
                        msg = Message('Password Reset OTP', recipients=[email])
                        msg.body = f'Your OTP for password reset is {otp}. It is valid for 5 minutes.'
                        mail.send(msg)

                except Exception as e:
                    logger.error(f"Error sending professional password reset email: {e}", exc_info=True)
                    # Fallback to simple email
                    msg = Message('Password Reset OTP', recipients=[email])
                    msg.body = f'Your OTP for password reset is {otp}. It is valid for 5 minutes.'
                    mail.send(msg)

                # Store OTP data in session
                session['reset_data'] = {
                    'email': email,
                    'otp': otp,
                    'otp_timestamp': datetime.now().timestamp()
                }
                flash('OTP has been sent to your email.', 'success')
                return redirect(url_for('reset_password'))

        except Exception as e:
            logger.error(f'Error: {str(e)}', exc_info=True)
            flash(f'Error: {str(e)}', 'error')
            return redirect(url_for('forgot_password'))
        finally:
            connection.close()

    return render_template('forgot_password.html')

@app.route('/reset_password', methods=['GET', 'POST'])
@limiter.limit("15 per minute")  # More reasonable rate limiting for password reset
def reset_password():
    if 'reset_data' not in session:
        flash('Please initiate password reset first', 'error')
        return redirect(url_for('forgot_password'))

    if request.method == 'POST':
        try:
            user_otp = request.form.get('otp', '').strip()
            new_password = request.form.get('new_password', '').strip()
            confirm_password = request.form.get('confirm_password', '').strip()
            reset_data = session.get('reset_data')

            # Validate inputs
            if not user_otp:
                flash('OTP is required', 'error')
                return redirect(url_for('reset_password'))

            if not new_password:
                flash('New password is required', 'error')
                return redirect(url_for('reset_password'))

            if not confirm_password:
                flash('Please confirm your password', 'error')
                return redirect(url_for('reset_password'))

            if new_password != confirm_password:
                flash('Passwords do not match', 'error')
                return redirect(url_for('reset_password'))

            # Validate password length
            if len(new_password) < 6:
                flash('Password must be at least 6 characters long', 'error')
                return redirect(url_for('reset_password'))

            new_password = new_password.encode('utf-8')
            confirm_password = confirm_password.encode('utf-8')

            # Verify OTP
            if secure_otp.verify_totp(user_otp, valid_window=5) and reset_data['otp'] == user_otp:
                try:
                    connection = get_db_connection()
                    with connection.cursor() as cursor:
                        # Validate new password strength
                        is_valid, error_msg = input_validator.validate_password_strength(new_password.decode('utf-8'))
                        if not is_valid:
                            flash(error_msg, 'error')
                            return redirect(url_for('reset_password'))

                        # Update password using secure hashing
                        password_hash = secure_auth.hash_password(new_password.decode('utf-8'))
                        sql = "UPDATE users SET password_hash = %s WHERE email = %s"
                        cursor.execute(sql, (password_hash, reset_data['email']))
                        connection.commit()

                        flash('Password reset successful! Please login with your new password.', 'success')
                        session.pop('reset_data', None)
                        return redirect(url_for('login'))

                except Exception as e:
                    logger.error(f'Error: {str(e)}', exc_info=True)
                    flash(f'Error: {str(e)}', 'error')
                    return redirect(url_for('reset_password'))
                finally:
                    connection.close()
            else:
                flash('Invalid or expired OTP', 'error')
                return redirect(url_for('reset_password'))

        except Exception as e:
            logger.error(f'Reset password error: {str(e)}', exc_info=True)
            flash('An error occurred during password reset. Please try again.', 'error')
            return redirect(url_for('reset_password'))

    return render_template('reset_password.html')

@app.route('/debug/session')
def debug_session():
    """Debug route to check session state - remove in production"""
    if os.getenv('FLASK_ENV') != 'development':
        return jsonify({'error': 'Not available in production'}), 404

    session_data = dict(session)
    # Remove sensitive data
    if 'reset_data' in session_data and 'otp' in session_data['reset_data']:
        session_data['reset_data']['otp'] = '***HIDDEN***'

    return jsonify({
        'session': session_data,
        'has_reset_data': 'reset_data' in session,
        'reset_data_keys': list(session.get('reset_data', {}).keys()) if 'reset_data' in session else []
    })

@app.route('/api/rate-limit-status')
@limiter.exempt  # Exempt this endpoint from rate limiting
def rate_limit_status():
    """Check current rate limit status for debugging"""
    if os.getenv('FLASK_ENV') != 'development':
        return jsonify({'error': 'Not available in production'}), 404

    try:
        # Get current rate limit info
        client_ip = get_client_ip()
        return jsonify({
            'client_ip': client_ip,
            'rate_limits': {
                'login': '10 per minute',
                'signup': '5 per minute',
                'default': '200 per day, 50 per hour'
            },
            'message': 'Rate limit status check'
        })
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@app.route('/health')
@limiter.exempt  # Health checks should not be rate limited
def health_check():
    """Application health check endpoint"""
    try:
        # Test database connection
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
        conn.close()
        db_status = "healthy"
    except Exception as e:
        logger.error(f"Database health check failed: {e}")
        db_status = "unhealthy"

    # Check Redis connection if configured
    redis_status = "not_configured"
    redis_url = os.getenv('REDIS_URL')
    if redis_url:
        try:
            import redis
            r = redis.from_url(redis_url)
            r.ping()
            redis_status = "healthy"
        except Exception as e:
            logger.error(f"Redis health check failed: {e}")
            redis_status = "unhealthy"

    health_data = {
        'status': 'healthy' if db_status == 'healthy' else 'degraded',
        'timestamp': datetime.now().isoformat(),
        'version': '1.0.0',
        'environment': os.getenv('FLASK_ENV', 'unknown'),
        'components': {
            'database': db_status,
            'redis': redis_status,
            'rate_limiter': 'healthy'
        }
    }

    status_code = 200 if health_data['status'] == 'healthy' else 503
    return jsonify(health_data), status_code

@app.route('/change_password', methods=['POST'])
def change_password():
    if 'user_id' not in session:
        return jsonify({'status': 'error', 'message': 'Please login first'}), 401

    user_id = session['user_id']
    data = request.form
    old_password = data.get('old_password', '').encode('utf-8')
    new_password = data.get('new_password', '').encode('utf-8')
    confirm_password = data.get('confirm_password', '').encode('utf-8')

    if not all([old_password, new_password, confirm_password]):
        return jsonify({'status': 'error', 'message': 'All fields are required'}), 400

    if new_password != confirm_password:
        return jsonify({'status': 'error', 'message': 'New passwords do not match'}), 400

    # Validate new password strength
    is_valid, error_msg = input_validator.validate_password_strength(new_password.decode('utf-8'))
    if not is_valid:
        return jsonify({'status': 'error', 'message': error_msg}), 400

    try:
        connection = get_db_connection()
        with connection.cursor(dictionary=True) as cursor:
            sql = "SELECT password_hash FROM users WHERE id = %s"
            cursor.execute(sql, (user_id,))
            user = cursor.fetchone()

            if not user or not secure_auth.verify_password(old_password.decode('utf-8'), user['password_hash']):
                return jsonify({'status': 'error', 'message': 'Incorrect old password'}), 401

            password_hash = secure_auth.hash_password(new_password.decode('utf-8'))
            sql = "UPDATE users SET password_hash = %s WHERE id = %s"
            cursor.execute(sql, (password_hash, user_id))
            connection.commit()

            return jsonify({'status': 'success', 'message': 'Password changed successfully'})
    except Exception as e:
        logger.error(f"Error in change_password: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': 'Password change failed. Please try again.'}), 500
    finally:
        connection.close()

@app.route('/dashboard')
@login_required
@limiter.limit("10 per minute")
def dashboard():
    if 'user_id' not in session:
        flash('Please login first', 'error')
        return redirect(url_for('login'))
    
    user_id = session['user_id']
    conn = get_db_connection()
    try:
        with conn.cursor(dictionary=True) as cursor:
            # Fetch bookings with detailed test information
            cursor.execute("""
                SELECT b.*, t.TestName, t.TestCode, t.DepartmentName, t.TestAmount, t.SampleType, t.TargetTAT 
                FROM bookings b 
                JOIN testdetails t ON b.test_id = t.SrNo 
                WHERE b.user_id = %s
            """, (user_id,))
            bookings = cursor.fetchall()

            # Fetch reports from patient_report table
            cursor.execute("""
                SELECT pr.*, b.test_id, t.TestName,
                       au1.name as sent_by_name,
                       au2.name as verified_by_name
                FROM patient_report pr
                JOIN bookings b ON pr.booking_id = b.id 
                JOIN testdetails t ON b.test_id = t.SrNo 
                LEFT JOIN admin_users au1 ON pr.sent_by_receptionist_id = au1.id
                LEFT JOIN admin_users au2 ON pr.verified_by_admin_id = au2.id
                WHERE pr.patient_id = %s
                ORDER BY pr.sent_at DESC
            """, (user_id,))
            reports = cursor.fetchall()

            # Fetch user profile
            cursor.execute("SELECT * FROM user_profiles WHERE user_id = %s", (user_id,))
            profile = cursor.fetchone()

            # Fetch email from users table
            cursor.execute("SELECT email FROM users WHERE id = %s", (user_id,))
            user = cursor.fetchone()
            email = user['email'] if user else ''

            # Fetch payments
            cursor.execute("""
                SELECT p.*, b.test_id, t.TestName 
                FROM payments p 
                JOIN bookings b ON p.booking_id = b.id 
                JOIN testdetails t ON b.test_id = t.SrNo 
                WHERE b.user_id = %s
            """, (user_id,))
            payments = cursor.fetchall()

    except Exception as e:
        logger.error(f"Dashboard error: {str(e)}", exc_info=True)
        flash('Error loading dashboard', 'error')
        return redirect(url_for('home'))
    finally:
        conn.close()

    return render_template('customer_dashboard.html',
                         bookings=bookings,
                         reports=reports,
                         profile=profile,
                         email=email,
                         payments=payments)

# Enhanced security headers
@app.after_request
def add_security_headers(response):
    # Comprehensive Content Security Policy
    csp_directives = [
        "default-src 'self'",
        "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://www.google.com https://www.gstatic.com https://cdnjs.cloudflare.com https://fonts.googleapis.com https://code.jquery.com https://checkout.razorpay.com",
        "style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com https://fonts.googleapis.com",
        "font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com",
        "img-src 'self' data: https:",
        "connect-src 'self' https://www.google.com https://checkout.razorpay.com https://api.razorpay.com https://lumberjack.razorpay.com",
        "frame-src 'self' https://www.google.com https://api.razorpay.com https://checkout.razorpay.com",
        "object-src 'none'",
        "base-uri 'self'",
        "form-action 'self'",
        "frame-ancestors 'none'",
        "upgrade-insecure-requests"
    ]

    response.headers['Content-Security-Policy'] = "; ".join(csp_directives)

    # Security headers
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'DENY'  # More restrictive than SAMEORIGIN
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'
    response.headers['Permissions-Policy'] = 'geolocation=(), microphone=(), camera=()'

    # HSTS (HTTP Strict Transport Security) for HTTPS
    if request.is_secure or os.getenv('FLASK_ENV') == 'production':
        response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload'

    # Prevent caching of sensitive pages
    if request.endpoint in ['login', 'dashboard', 'admin.dashboard']:
        response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
        response.headers['Pragma'] = 'no-cache'
        response.headers['Expires'] = '0'

    return response

# Initialize cart in session and handle malformed requests
@app.before_request
def initialize_cart():
    # Handle malformed requests early
    try:
        # Check for SSL handshake attempts on HTTP port
        if request.headers.get('Content-Type') == 'application/octet-stream':
            logger.warning(f"SSL handshake attempt on HTTP port from {request.remote_addr}")
            return jsonify({'error': 'SSL not supported on this port'}), 400

        # Check for malformed HTTP requests
        if not hasattr(request, 'method') or not request.method:
            logger.warning(f"Malformed HTTP request from {request.remote_addr}")
            return jsonify({'error': 'Malformed request'}), 400

    except Exception as e:
        logger.warning(f"Request validation error from {request.remote_addr}: {str(e)}")
        return jsonify({'error': 'Invalid request'}), 400

    if 'cart' not in session:
        session['cart'] = {}

    # Only validate JSON for POST/PUT requests that claim to have JSON content
    if request.method in ['POST', 'PUT'] and request.is_json:
        try:
            # Try to get JSON, but don't fail if it's empty for GET requests
            json_data = request.get_json(silent=True)
            if json_data is None and request.content_length and request.content_length > 0:
                return jsonify({'status': 'error', 'message': 'Invalid JSON data'}), 400
        except Exception:
            return jsonify({'status': 'error', 'message': 'Invalid JSON data'}), 400

# CSRF exemption decorator for API endpoints
def csrf_exempt(view):
    """Exempt specific views from CSRF protection (use sparingly)"""
    setattr(view, '_csrf_exempt', True)
    return view

# Secure API endpoints that need CSRF exemption
@csrf.exempt
def api_csrf_exempt(view):
    """Exempt API endpoints from CSRF but require other authentication"""
    return view

@app.route('/cart/add', methods=['POST'])
@login_required
@limiter.limit("10 per minute")
def add_to_cart():
    try:
        data = request.get_json()
        
        if not data or 'test_id' not in data:
            return jsonify({'status': 'error', 'message': 'Invalid request data'}), 400

        test_id = data['test_id']
        conn = get_db_connection()
        
        with conn.cursor(dictionary=True) as cursor:
            # Get test details
            cursor.execute("""
                SELECT SrNo, TestName, TestCode, DepartmentName, SampleType, 
                       TestAmount, OutsourceAmount, TargetTAT, TestCategory, active 
                FROM testdetails 
                WHERE SrNo = %s AND active = TRUE
            """, (test_id,))
            test = cursor.fetchone()
            
            if not test:
                return jsonify({'status': 'error', 'message': 'Test not found or inactive'}), 404

            cart = session.get('cart', {})
            test_id_str = str(test_id)

            if test_id_str not in cart:
                cart[test_id_str] = {
                    'test_id': test_id,
                    'name': test['TestName'],
                    'price': float(test['TestAmount']),
                    'quantity': 1
                }
                session['cart'] = cart
                session.modified = True

            cart_items = list(cart.values())
            total = sum(item['price'] * item['quantity'] for item in cart_items)

            return jsonify({
                'status': 'success',
                'message': 'Test added to cart',
                'cart': cart_items,
                'total': total,
                'count': len(cart_items)
            })

    except Exception as e:
        logger.error(f"Cart add error: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': str(e)}), 500
    finally:
        if 'conn' in locals():
            conn.close()

@app.route('/cart/update/<int:test_id>', methods=['POST'])
@login_required
@limiter.limit("10 per minute")
def update_cart(test_id):
    if 'user_id' not in session:
        return jsonify({'status': 'error', 'message': 'Please login first'}), 401

    data = request.get_json()
    action = data.get('action')

    if action not in ['increment', 'decrement', 'remove']:
        return jsonify({'status': 'error', 'message': 'Invalid action'}), 400

    if 'cart' not in session:
        session['cart'] = {}

    test_id_str = str(test_id)
    if test_id_str in session['cart']:
        if action == 'increment':
            session['cart'][test_id_str]['quantity'] += 1
        elif action == 'decrement':
            session['cart'][test_id_str]['quantity'] -= 1
            if session['cart'][test_id_str]['quantity'] <= 0:
                del session['cart'][test_id_str]
        elif action == 'remove':
            del session['cart'][test_id_str]

        session.modified = True

    cart_items = list(session['cart'].values())
    total = sum(item['price'] * item['quantity'] for item in cart_items)

    return jsonify({
        'status': 'success',
        'cart': cart_items,
        'total': total,
        'count': len(cart_items)
    })

@app.route('/cart', methods=['GET'])
@login_required
@limiter.limit("20 per minute")
def get_cart():
    if 'user_id' not in session:
        return jsonify({'status': 'error', 'message': 'Please login first'}), 401

    cart_items = list(session.get('cart', {}).values())
    total = sum(item['price'] * item['quantity'] for item in cart_items)

    return jsonify({
        'status': 'success',
        'cart': cart_items,
        'total': total,
        'count': len(cart_items)
    })

# API v1 endpoints with JWT authentication
@app.route('/api/v1/cart', methods=['GET'])
@jwt_required
@api_error_handler
@limiter.limit("30 per minute")
def api_get_cart():
    """API endpoint for getting cart with JWT authentication"""
    user_id = request.current_user_id

    # Get cart from session or database based on user_id
    # For now, using session-based cart
    cart_items = list(session.get('cart', {}).values())
    total = sum(item['price'] * item['quantity'] for item in cart_items)

    return jsonify({
        'status': 'success',
        'data': {
            'cart': cart_items,
            'total': total,
            'count': len(cart_items)
        },
        'api_version': 'v1'
    })

@app.route('/api/v1/auth/refresh', methods=['POST'])
@validate_json_request(['refresh_token'])
@api_error_handler
@limiter.limit("10 per minute")
def api_refresh_token():
    """API endpoint for refreshing access tokens"""
    data = request.get_json()
    refresh_token = data.get('refresh_token')

    from api_security import APIAuth
    new_access_token = APIAuth.refresh_access_token(refresh_token)

    if not new_access_token:
        return jsonify({
            'status': 'error',
            'message': 'Invalid or expired refresh token'
        }), 401

    return jsonify({
        'status': 'success',
        'data': {
            'access_token': new_access_token
        },
        'api_version': 'v1'
    })

# Payment routes with CSRF exemption
@app.route('/book_tests', methods=['POST'])
@login_required
@limiter.limit("5 per minute")
def book_tests():
    if 'user_id' not in session:
        return jsonify({'status': 'error', 'message': 'Please login first'}), 401

    try:
        data = request.get_json()
        if not data:
            return jsonify({'status': 'error', 'message': 'Invalid request data'}), 400

        tests = data.get('tests', [])
        total_amount = float(data.get('total_amount', 0))
        user_id = session['user_id']

        if not tests:
            return jsonify({'status': 'error', 'message': 'No tests to book'}), 400

        if total_amount <= 0:
            return jsonify({'status': 'error', 'message': 'Invalid amount'}), 400

        # Create Razorpay order
        try:
            order_amount = int(total_amount * 100)  # Convert to paise
            order_currency = 'INR'
            # Generate shorter receipt ID (max 40 chars)
            receipt = f'rcpt_{uuid.uuid4().hex[:16]}'
            
            # Add notes for better tracking
            notes = {
                'user_id': str(user_id),
                'test_count': str(len(tests))
            }
            
            razorpay_order = razorpay_client.order.create({
                'amount': order_amount,
                'currency': order_currency,
                'receipt': receipt,
                'notes': notes
            })

            if not razorpay_order.get('id'):
                raise ValueError('Order ID not received from Razorpay')

            # Store order details in session
            session['razorpay_order'] = {
                'order_id': razorpay_order['id'],
                'tests': tests,
                'amount': total_amount,
                'receipt': receipt
            }

            return jsonify({
                'status': 'success',
                'order_id': razorpay_order['id'],
                'amount': order_amount,
                'currency': order_currency,
                'key': os.getenv('RAZORPAY_KEY_ID')
            })

        except razorpay.errors.BadRequestError as e:
            logger.error(f"Razorpay BadRequestError: {str(e)}", exc_info=True)
            error_message = str(e).split(':')[-1].strip()
            return jsonify({'status': 'error', 'message': f'Payment gateway error: {error_message}'}), 400
            
        except Exception as e:
            logger.error(f"Razorpay Order Creation Error: {str(e)}", exc_info=True)
            return jsonify({'status': 'error', 'message': 'Failed to create payment order'}), 500

    except Exception as e:
        logger.error(f"Book Tests Error: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': 'An unexpected error occurred'}), 500

@app.route('/store_billing_info', methods=['POST'])
@login_required
@limiter.limit("5 per minute")
def store_billing_info():
    if 'user_id' not in session:
        return jsonify({'status': 'error', 'message': 'Please login first'}), 401

    if not request.is_json:
        return jsonify({'status': 'error', 'message': 'Invalid request format'}), 400

    try:
        billing_info = request.get_json()
        
        # Ensure all required fields are present
        required_fields = ['fullName', 'email', 'phone', 'address', 'city', 'pincode']
        missing_fields = [field for field in required_fields if not billing_info.get(field)]
        
        if missing_fields:
            return jsonify({
                'status': 'error',
                'message': f'Missing required fields: {", ".join(missing_fields)}'
            }), 400

        # Store in session
        session['billing_info'] = billing_info
        
        return jsonify({'status': 'success', 'message': 'Billing information stored successfully'})
    except Exception as e:
        logger.error(f"Error storing billing info: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': 'Failed to store billing information'}), 500

@app.route('/verify_payment', methods=['POST'])
@login_required
@limiter.limit("5 per minute")
@csrf.exempt  # Payment verification needs CSRF exemption for Razorpay callback
def verify_payment():
    if 'user_id' not in session:
        return jsonify({'status': 'error', 'message': 'User not logged in'}), 401

    if not request.is_json:
        return jsonify({'status': 'error', 'message': 'Invalid request format'}), 400

    try:
        data = request.get_json()

        razorpay_payment_id = data.get('payment_id')
        razorpay_order_id = data.get('order_id')
        razorpay_signature = data.get('signature')

        if not all([razorpay_payment_id, razorpay_order_id, razorpay_signature]):
            return jsonify({'status': 'error', 'message': 'Missing payment parameters'}), 400

        # Get order details from session
        order_details = session.get('razorpay_order')
        if not order_details:
            return jsonify({'status': 'error', 'message': 'Order details not found'}), 400

        # Get billing information from session
        billing_info = session.get('billing_info')
        if not billing_info:
            return jsonify({'status': 'error', 'message': 'Billing information not found'}), 400

        # Verify payment signature
        params_dict = {
            'razorpay_payment_id': razorpay_payment_id,
            'razorpay_order_id': razorpay_order_id,
            'razorpay_signature': razorpay_signature
        }

        try:
            razorpay_client.utility.verify_payment_signature(params_dict)
        except Exception as e:
            logger.error(f"Signature verification failed: {str(e)}", exc_info=True)
            return jsonify({'status': 'error', 'message': 'Invalid payment signature'}), 400

        # Process the order
        conn = get_db_connection()
        try:
            with conn.cursor() as cursor:
                booking_ids = []
                tests = order_details.get('tests', [])
                if not tests:
                    logger.error("No tests found in order details")
                    return
                user_id = session['user_id']
                logger.info(f"Processing order for user {user_id} with {len(tests)} tests")

                for test in tests:
                    barcode = str(uuid.uuid4())[:8].upper()
                    booking_date = datetime.now().date()
                    appointment_time = datetime.now().time()
                    
                    # Insert booking with address information
                    cursor.execute("""
                        INSERT INTO bookings 
                        (user_id, test_id, booking_date, appointment_time, booking_status, barcode,
                         address_line1, address_line2, city, state, postal_code, country)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """, (
                        user_id,
                        test['test_id'],
                        booking_date,
                        appointment_time,
                        'pending',
                        barcode,
                        billing_info.get('address', ''),  # address_line1
                        '',  # address_line2 (optional)
                        billing_info.get('city', ''),
                        billing_info.get('state', ''),  # Use state from billing info
                        billing_info.get('pincode', ''),
                        'India'  # Default country
                    ))
                    
                    booking_id = cursor.lastrowid
                    booking_ids.append(booking_id)

                    # Generate unique transaction ID for each test
                    unique_transaction_id = f"{razorpay_payment_id}_{test['test_id']}"

                    # Insert payment record with unique transaction ID
                    cursor.execute("""
                        INSERT INTO payments 
                        (booking_id, amount, payment_method, transaction_id, payment_status)
                        VALUES (%s, %s, %s, %s, %s)
                    """, (
                        booking_id,
                        test['price'],
                        'RazorPay',
                        unique_transaction_id,
                        'paid'
                    ))

                # Apply coupon if one is active
                active_coupon = session.get('active_coupon')
                if active_coupon and booking_ids:
                    # Apply coupon to the first booking
                    cursor.execute("""
                        INSERT INTO coupon_usage (coupon_id, user_id, booking_id)
                        VALUES (%s, %s, %s)
                    """, (active_coupon['id'], user_id, booking_ids[0]))

                    # Update coupon status
                    cursor.execute("""
                        UPDATE coupons 
                        SET status = 'Used' 
                        WHERE id = %s
                    """, (active_coupon['id'],))

                conn.commit()
                logger.info(f"Successfully created {len(booking_ids)} bookings")

                # Clear session data
                session.pop('razorpay_order', None)
                session.pop('cart', None)
                session.pop('billing_info', None)
                session.pop('active_coupon', None)  # Clear active coupon
                
                return jsonify({
                    'status': 'success',
                    'message': 'Payment verified and booking completed',
                    'booking_ids': booking_ids
                })
        except Exception as e:
            logger.error(f"Database error: {str(e)}", exc_info=True)
            conn.rollback()
            return jsonify({'status': 'error', 'message': f'Failed to process booking: {str(e)}'}), 500
        finally:
            conn.close()
    except Exception as e:
        logger.error(f"Payment verification error: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/verify_payment_link', methods=['POST'])
@limiter.limit("5 per minute")
@csrf.exempt  # Payment verification needs CSRF exemption for Razorpay callback
def verify_payment_link():
    """Verify payment for payment links"""
    if not request.is_json:
        return jsonify({'status': 'error', 'message': 'Invalid request format'}), 400

    try:
        data = request.get_json()

        razorpay_payment_id = data.get('payment_id')
        razorpay_order_id = data.get('order_id')
        razorpay_signature = data.get('signature')
        link_id = data.get('link_id')

        if not all([razorpay_payment_id, razorpay_order_id, razorpay_signature, link_id]):
            return jsonify({'status': 'error', 'message': 'Missing payment parameters'}), 400

        # Verify payment signature
        params_dict = {
            'razorpay_payment_id': razorpay_payment_id,
            'razorpay_order_id': razorpay_order_id,
            'razorpay_signature': razorpay_signature
        }

        try:
            razorpay_client.utility.verify_payment_signature(params_dict)
        except Exception as e:
            logger.error(f"Signature verification failed: {str(e)}", exc_info=True)
            return jsonify({'status': 'error', 'message': 'Invalid payment signature'}), 400

        # Update payment link status
        conn = get_db_connection()
        try:
            with conn.cursor(dictionary=True) as cursor:
                # Mark payment link as used
                cursor.execute("""
                    UPDATE payment_links
                    SET status = 'used', used_at = NOW()
                    WHERE link_id = %s
                """, (link_id,))

                # Create a payment record for the link payment
                cursor.execute("""
                    INSERT INTO payments
                    (amount, payment_method, transaction_id, payment_status, payment_date)
                    VALUES (%s, %s, %s, %s, NOW())
                """, (
                    data.get('amount', 0),
                    'RazorPay',
                    razorpay_payment_id,
                    'paid'
                ))

                conn.commit()

                return jsonify({
                    'status': 'success',
                    'message': 'Payment verified successfully'
                })

        except Exception as e:
            logger.error(f"Database error in payment link verification: {str(e)}", exc_info=True)
            conn.rollback()
            return jsonify({'status': 'error', 'message': 'Failed to process payment'}), 500
        finally:
            conn.close()

    except Exception as e:
        logger.error(f"Payment link verification error: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': str(e)}), 500

@app.route('/cancel_booking/<int:booking_id>', methods=['POST'])
@login_required
@limiter.limit("5 per minute")
def cancel_booking(booking_id):
    if 'user_id' not in session:
        return jsonify({'status': 'error', 'message': 'Please login first'}), 401

    user_id = session['user_id']
    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            # Verify the booking belongs to the user and is cancellable
            cursor.execute("""
                SELECT booking_status FROM bookings 
                WHERE id = %s AND user_id = %s
            """, (booking_id, user_id))
            booking = cursor.fetchone()

            if not booking:
                return jsonify({'status': 'error', 'message': 'Booking not found'}), 404

            if booking['booking_status'] != 'pending':
                return jsonify({'status': 'error', 'message': 'Only pending bookings can be cancelled'}), 400

            # Update booking status to cancelled
            cursor.execute("""
                UPDATE bookings 
                SET booking_status = 'cancelled' 
                WHERE id = %s
            """, (booking_id,))
            conn.commit()

            return jsonify({'status': 'success', 'message': 'Booking cancelled successfully'})

    except Exception as e:
        conn.rollback()
        return jsonify({'status': 'error', 'message': str(e)}), 500
    finally:
        conn.close()

@app.route('/edit_profile', methods=['POST'])
@login_required
@limiter.limit("5 per minute")
def edit_profile():
    if 'user_id' not in session:
        flash('Please login first', 'error')
        return redirect(url_for('login'))

    user_id = session['user_id']
    data = request.form

    # Validate and sanitize inputs
    name = input_validator.sanitize_input(data.get('name', ''))
    if not name:
        flash('Name is required', 'error')
        return redirect(url_for('dashboard'))

    first_name = name.split()[0]
    last_name = ' '.join(name.split()[1:]) if len(name.split()) > 1 else ''

    age = data.get('age', '')
    gender = input_validator.sanitize_input(data.get('gender', ''))
    email = data.get('email', '')
    phone = data.get('phone', '')

    # Validate email format
    if email and not input_validator.validate_email(email):
        flash('Please enter a valid email address', 'error')
        return redirect(url_for('dashboard'))

    # Validate phone format
    if phone and not input_validator.validate_phone(phone):
        flash('Please enter a valid phone number', 'error')
        return redirect(url_for('dashboard'))

    # Validate age
    if age and not age.isdigit():
        flash('Please enter a valid age', 'error')
        return redirect(url_for('dashboard'))

    # Validate gender
    if gender and gender not in ['Male', 'Female', 'Other']:
        flash('Please select a valid gender', 'error')
        return redirect(url_for('dashboard'))

    # Calculate date of birth from age
    date_of_birth = None
    if age and age.isdigit():
        current_year = datetime.now().year
        birth_year = current_year - int(age)
        date_of_birth = datetime(birth_year, 1, 1).date()  # Default to Jan 1st

    conn = get_db_connection()
    try:
        with conn.cursor() as cursor:
            # Update email in users table
            cursor.execute("UPDATE users SET email = %s WHERE id = %s", (email, user_id))

            # Update user profile
            cursor.execute("SELECT * FROM user_profiles WHERE user_id = %s", (user_id,))
            if cursor.fetchone():
                cursor.execute("""
                    UPDATE user_profiles 
                    SET first_name = %s, last_name = %s, phone = %s, date_of_birth = %s, gender = %s 
                    WHERE user_id = %s
                """, (first_name, last_name, phone, date_of_birth, gender, user_id))
            else:
                cursor.execute("""
                    INSERT INTO user_profiles (user_id, first_name, last_name, phone, date_of_birth, gender) 
                    VALUES (%s, %s, %s, %s, %s, %s)
                """, (user_id, first_name, last_name, phone, date_of_birth, gender))

            conn.commit()
            flash('Profile updated successfully', 'success')
            return jsonify({'status': 'success', 'message': 'Profile updated successfully'})
    except Exception as e:
        flash(f'Error: {str(e)}', 'error')
        return jsonify({'status': 'error', 'message': str(e)}), 500
    finally:
        conn.close()

@app.route('/send_confirmation_email', methods=['POST'])
def send_confirmation_email():
    if not request.is_json:
        return jsonify({'status': 'error', 'message': 'Invalid request format'}), 400

    data = request.get_json()
    email = data.get('email')
    name = data.get('name', 'Customer')
    amount = data.get('amount')
    tests = data.get('tests', [])

    if not email or not amount:
        return jsonify({'status': 'error', 'message': 'Missing email or amount'}), 400

    try:
        # Use the new professional email service
        from email_service import get_email_service
        service = get_email_service()

        # Format tests for the professional template
        formatted_tests = []
        if tests and isinstance(tests, list):
            for test in tests:
                formatted_tests.append({
                    'name': test.get('name', 'Test'),
                    'code': test.get('code', ''),
                    'amount': float(test.get('price', 0)),
                    'quantity': test.get('quantity', 1)
                })

        # Generate a transaction ID
        transaction_id = f"CVB{uuid.uuid4().hex[:12].upper()}"

        success = service.send_payment_confirmation(
            recipient=email,
            customer_name=name,
            amount=float(amount),
            transaction_id=transaction_id,
            payment_method="Online",
            tests=formatted_tests,
            payment_date=datetime.now()
        )

        if success:
            return jsonify({'status': 'success', 'message': 'Professional confirmation email sent'})
        else:
            # Fallback to simple email if professional service fails
            raise Exception("Professional email service failed")

    except Exception as e:
        logger.error(f"Error sending professional confirmation email: {e}", exc_info=True)
        # Fallback to simple email
        try:
            # Format test details for fallback email
            test_lines = []
            if tests and isinstance(tests, list):
                test_lines.append("Test Details:")
                for idx, test in enumerate(tests, 1):
                    test_lines.append(
                        f"{idx}. {test.get('name', 'Test')} (x{test.get('quantity', 1)}): ₹{test.get('price', 0)}"
                    )
                test_lines.append("")  # Add an empty line

            msg = Message(
                subject='CVBioLabs - Payment Confirmation',
                recipients=[email],
                body=(
                    f"Dear {name},\n\n"
                    f"Thank you for your payment of ₹{amount:.2f}.\n"
                    f"Your test booking is confirmed.\n\n"
                    + ("\n".join(test_lines) if test_lines else "")
                    + "Regards,\nCVBioLabs Team"
                )
            )
            mail.send(msg)
            return jsonify({'status': 'success', 'message': 'Confirmation email sent'})
        except Exception as fallback_error:
            logger.error(f"Fallback email also failed: {fallback_error}", exc_info=True)
            return jsonify({'status': 'error', 'message': 'Failed to send confirmation email'}), 500

@app.route('/logout')
@login_required
def logout():
    session.clear()
    logout_user()
    flash('You have been logged out', 'success')
    return redirect(url_for('home'))

@app.route('/test')
def test():
    try:
        # Test database connection
        conn = get_db_connection()
        if not conn:
            logger.error("Database connection failed")
            flash('Database connection error', 'error')
            return redirect(url_for('home'))

        logger.info("Database connection successful")

        with conn.cursor(dictionary=True) as cursor:
            # Test the query directly
            test_query = """
                SELECT 
                    SrNo,
                    TestName,
                    TestCode,
                    DepartmentName,
                    SampleType,
                    TestAmount,
                    OutsourceAmount,
                    TargetTAT,
                    TestCategory,
                    active
                FROM testdetails 
                WHERE active = TRUE 
                LIMIT 1
            """
            
            cursor.execute(test_query)
            test_result = cursor.fetchone()
            

            if not test_result:
                logger.warning("No test data found in database")
                flash('No test data available', 'warning')
                return render_template('test.html', 
                                    tests=[],
                                    departments=[],
                                    total_count=0)

            # Get total count
            cursor.execute("SELECT COUNT(*) as total FROM testdetails WHERE active = TRUE")
            total_count = cursor.fetchone()['total']
            

            # Get departments
            cursor.execute("""
                SELECT DISTINCT DepartmentName 
                FROM testdetails 
                WHERE DepartmentName IS NOT NULL 
                AND active = TRUE
            """)
            departments = [row['DepartmentName'] for row in cursor.fetchall()]
            

            # Get tests
            cursor.execute("""
                SELECT 
                    SrNo,
                    TestName,
                    TestCode,
                    DepartmentName,
                    SampleType,
                    TestAmount,
                    OutsourceAmount,
                    TargetTAT,
                    TestCategory,
                    active
                FROM testdetails 
                WHERE active = TRUE 
                ORDER BY TestName
                LIMIT 6
            """)
            tests = cursor.fetchall()
            

            if not tests:
                logger.warning("No tests found")
                return render_template('test.html', 
                                    tests=[],
                                    departments=[],
                                    total_count=0)

            # Format tests with explicit type checking
            formatted_tests = []
            for test in tests:
                try:
                    # Ensure all fields exist and have proper types
                    formatted_test = {
                        'SrNo': str(test.get('SrNo', 'N/A')),
                        'TestName': str(test.get('TestName', 'N/A')),
                        'TestCode': str(test.get('TestCode', 'N/A')),
                        'DepartmentName': str(test.get('DepartmentName', 'N/A')),
                        'SampleType': str(test.get('SampleType', 'N/A')),
                        'TestAmount': float(test.get('TestAmount', 0)) if test.get('TestAmount') is not None else 0.0,
                        'OutsourceAmount': float(test.get('OutsourceAmount', 0)) if test.get('OutsourceAmount') is not None else 0.0,
                        'TargetTAT': str(test.get('TargetTAT', 'N/A')),
                        'TestCategory': str(test.get('TestCategory', 'N/A')),
                        'active': bool(test.get('active', True))
                    }
                    formatted_tests.append(formatted_test)
                except Exception as e:
                    logger.error(f"Error formatting test {test}: {str(e)}", exc_info=True)
                    continue

            template_data = {
                'tests': formatted_tests,
                'departments': departments,
                'total_count': total_count
            }
            

            return render_template('test.html', **template_data)

    except Exception as e:
        logger.error(f"Error in test route: {str(e)}", exc_info=True)
        import traceback
        logger.error(f"Traceback: {traceback.format_exc()}")
        flash('Error loading tests', 'error')
        return redirect(url_for('home'))
    finally:
        if 'conn' in locals():
            conn.close()

@app.route('/payment_success')
def payment_success():
    flash('Payment successful! Your tests have been booked successfully.', 'success')
    return redirect(url_for('test'))

@app.route('/payment/link/<link_id>')
def payment_link(link_id):
    """Handle payment link access"""
    conn = get_db_connection()
    if not conn:
        flash('Service temporarily unavailable', 'error')
        return redirect(url_for('home'))

    try:
        with conn.cursor(dictionary=True) as cursor:
            # Get payment link details
            cursor.execute("""
                SELECT * FROM payment_links
                WHERE link_id = %s AND status = 'active' AND expiry_date > NOW()
            """, (link_id,))
            payment_link = cursor.fetchone()

            if not payment_link:
                flash('Payment link is invalid or has expired', 'error')
                return redirect(url_for('home'))

            # Create a simple payment page
            return render_template('payment_link.html',
                                 payment_link=payment_link,
                                 razorpay_key=os.getenv('RAZORPAY_KEY_ID'))

    except Exception as e:
        logger.error(f"Error accessing payment link: {str(e)}", exc_info=True)
        flash('Error accessing payment link', 'error')
        return redirect(url_for('home'))
    finally:
        conn.close()

@app.route('/payment/link/<link_id>/process', methods=['POST'])
@limiter.limit("5 per minute")
def process_payment_link(link_id):
    """Process payment from payment link"""
    conn = get_db_connection()
    if not conn:
        return jsonify({'error': 'Service temporarily unavailable'}), 500

    try:
        with conn.cursor(dictionary=True) as cursor:
            # Verify payment link is still valid
            cursor.execute("""
                SELECT * FROM payment_links
                WHERE link_id = %s AND status = 'active' AND expiry_date > NOW()
            """, (link_id,))
            payment_link = cursor.fetchone()

            if not payment_link:
                return jsonify({'error': 'Payment link is invalid or has expired'}), 400

            # Create Razorpay order
            try:
                order_amount = int(float(payment_link['amount']) * 100)  # Convert to paise
                order_currency = 'INR'
                receipt = f'link_{link_id[:16]}'

                razorpay_order = razorpay_client.order.create({
                    'amount': order_amount,
                    'currency': order_currency,
                    'receipt': receipt,
                    'notes': {
                        'payment_link_id': link_id,
                        'email': payment_link['email']
                    }
                })

                if not razorpay_order.get('id'):
                    raise ValueError('Order ID not received from Razorpay')

                return jsonify({
                    'status': 'success',
                    'order_id': razorpay_order['id'],
                    'amount': order_amount,
                    'currency': order_currency,
                    'key': os.getenv('RAZORPAY_KEY_ID'),
                    'email': payment_link['email']
                })

            except Exception as e:
                logger.error(f"Razorpay Order Creation Error for payment link: {str(e)}", exc_info=True)
                return jsonify({'error': 'Failed to create payment order'}), 500

    except Exception as e:
        logger.error(f"Error processing payment link: {str(e)}", exc_info=True)
        return jsonify({'error': 'An unexpected error occurred'}), 500
    finally:
        conn.close()

@app.route('/payment_error')
def payment_error():
    flash('Payment successful but there was an error sending the confirmation email. Please check your dashboard.', 'error')
    return redirect(url_for('test'))

@app.route('/load_more_tests')
def load_more_tests():
    try:
        offset = request.args.get('offset', 0, type=int)
        limit = request.args.get('limit', 10, type=int)
        
        conn = get_db_connection()
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT * FROM testdetails 
                WHERE active = TRUE 
                ORDER BY TestName
                LIMIT %s OFFSET %s
            """, (limit, offset))
            tests = cursor.fetchall()
            
            # Convert Decimal to float for JSON serialization
            for test in tests:
                if 'TestAmount' in test:
                    test['TestAmount'] = float(test['TestAmount'])
                if 'OutsourceAmount' in test:
                    test['OutsourceAmount'] = float(test['OutsourceAmount'])
            
            return jsonify({
                'status': 'success',
                'tests': tests,
                'has_more': len(tests) == limit
            })
            
    except Exception as e:
        logger.error(f"Error loading more tests: {str(e)}", exc_info=True)
        return jsonify({'status': 'error', 'message': 'Failed to load more tests'}), 500
    finally:
        if 'conn' in locals():
            conn.close()

@app.route('/blogs')
def blogs():
    return render_template('blogs.html')

@app.route('/test-csp')
def test_csp():
    """Test page for CSP and script loading issues"""
    return render_template('test_csp.html')

@app.route('/search_tests')
def search_tests():
    try:
        query = request.args.get('query', '').strip()
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 20, type=int)  # Default 20 tests per page
        department = request.args.get('department', '').strip()

        # Limit per_page to prevent abuse
        per_page = min(per_page, 50)
        offset = (page - 1) * per_page

        conn = get_db_connection()

        with conn.cursor(dictionary=True) as cursor:
            # Build WHERE conditions
            where_conditions = ["active = TRUE"]
            params = []

            if query:
                where_conditions.append("(TestName LIKE %s OR DepartmentName LIKE %s OR TestCategory LIKE %s OR TestCode LIKE %s)")
                search_param = f'%{query}%'
                params.extend([search_param, search_param, search_param, search_param])

            if department:
                where_conditions.append("DepartmentName = %s")
                params.append(department)

            where_clause = " AND ".join(where_conditions)

            # Get total count for pagination
            count_query = f"SELECT COUNT(*) as total FROM testdetails WHERE {where_clause}"
            cursor.execute(count_query, params)
            total_count = cursor.fetchone()['total']

            # Get paginated tests
            test_query = f"""
                SELECT SrNo, TestName, TestCode, DepartmentName, SampleType,
                       TestAmount, OutsourceAmount, TargetTAT, TestCategory, active
                FROM testdetails
                WHERE {where_clause}
                ORDER BY TestName
                LIMIT %s OFFSET %s
            """
            params.extend([per_page, offset])
            cursor.execute(test_query, params)
            tests = cursor.fetchall()

            # Format the test data
            formatted_tests = []
            for test in tests:
                formatted_test = {
                    'SrNo': str(test['SrNo']),
                    'TestName': test['TestName'] or 'N/A',
                    'TestCode': test['TestCode'] or 'N/A',
                    'DepartmentName': test['DepartmentName'] or 'N/A',
                    'SampleType': test['SampleType'] or 'N/A',
                    'TestAmount': float(test['TestAmount']) if test['TestAmount'] else 0.0,
                    'OutsourceAmount': float(test['OutsourceAmount']) if test['OutsourceAmount'] else 0.0,
                    'TargetTAT': test['TargetTAT'] or '-',
                    'TestCategory': test['TestCategory'] or '-',
                    'active': bool(test['active'])
                }
                formatted_tests.append(formatted_test)

            return jsonify({
                'tests': formatted_tests,
                'pagination': {
                    'page': page,
                    'per_page': per_page,
                    'total': total_count,
                    'pages': (total_count + per_page - 1) // per_page,
                    'has_next': page * per_page < total_count,
                    'has_prev': page > 1
                }
            })

    except Exception as e:
        logger.error(f"Error searching tests: {str(e)}", exc_info=True)
        return jsonify({'error': 'Failed to search tests'}), 500
    finally:
        if 'conn' in locals():
            conn.close()

@app.route('/book_test', methods=['POST'])
@login_required
def book_test():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'success': False, 'message': 'No data provided'}), 400

        test_id = data.get('test_id')
        if not test_id:
            return jsonify({'success': False, 'message': 'Test ID is required'}), 400

        conn = get_db_connection()
        with conn.cursor() as cursor:
            # Get test details
            cursor.execute("SELECT * FROM testdetails WHERE SrNo = %s", (test_id,))
            test = cursor.fetchone()
            
            if not test:
                return jsonify({'success': False, 'message': 'Test not found'}), 404

            # Check if test is active
            if not test.get('active', True):
                return jsonify({'success': False, 'message': 'This test is currently not available'}), 400

            # Generate unique barcode
            barcode = f"CVB{uuid.uuid4().hex[:8].upper()}"
            
            # Create booking
            cursor.execute("""
                INSERT INTO bookings 
                (user_id, test_id, booking_date, appointment_time, booking_status, barcode)
                VALUES (%s, %s, CURDATE(), CURTIME(), %s, %s)
            """, (current_user.id, test_id, 'pending', barcode))
            
            booking_id = cursor.lastrowid

            # Create payment record with correct enum value
            cursor.execute("""
                INSERT INTO payments 
                (booking_id, amount, payment_method, payment_status)
                VALUES (%s, %s, %s, %s)
            """, (booking_id, test['TestAmount'], 'Cash', 'pending'))

            conn.commit()
            
            return jsonify({
                'success': True,
                'message': 'Test booked successfully',
                'booking_id': booking_id,
                'barcode': barcode
            })
            
    except Exception as e:
        logger.error(f"Error booking test: {str(e)}", exc_info=True)
        if 'conn' in locals():
            conn.rollback()
        return jsonify({'success': False, 'message': f'Failed to book test: {str(e)}'}), 500
    finally:
        if 'conn' in locals():
            conn.close()

@app.route('/validate_coupon', methods=['POST'])
@csrf_exempt
def validate_coupon():
    if 'user_id' not in session:
        return jsonify({
            'status': 'error',
            'message': 'Please login first'
        }), 401

    try:
        if not request.is_json:
            return jsonify({
                'status': 'error',
                'message': 'Invalid request format'
            }), 400

        data = request.get_json()
        coupon_code = data.get('coupon_code')
        if not coupon_code:
            return jsonify({
                'status': 'error',
                'message': 'Coupon code is required'
            }), 400

        user_id = session['user_id']

        conn = get_db_connection()
        with conn.cursor(dictionary=True) as cursor:  # <-- Use dictionary cursor
            # Check if coupon exists and is valid
            cursor.execute("""
                SELECT c.*, 
                       CASE WHEN cu.id IS NOT NULL THEN 1 ELSE 0 END as is_used
                FROM coupons c
                LEFT JOIN coupon_usage cu ON c.id = cu.coupon_id AND cu.user_id = %s
                WHERE c.code = %s AND c.status = 'Active' AND c.expiry_date >= CURDATE()
            """, (user_id, coupon_code))
            
            coupon = cursor.fetchone()

            if not coupon:
                return jsonify({
                    'status': 'error',
                    'message': 'Invalid or expired coupon code'
                }), 400

            if coupon['is_used']:
                return jsonify({
                    'status': 'error',
                    'message': 'You have already used this coupon'
                }), 400



            # Store coupon in session for later use
            session['active_coupon'] = {
                'id': coupon['id'],
                'code': coupon['code'],
                'discount_amount': float(coupon['discount_amount'])
            }

            return jsonify({
                'status': 'success',
                'message': 'Coupon applied successfully',
                'discount': float(coupon['discount_amount']),
                'code': coupon['code']
            })

    except Exception as e:
        logger.error(f"Coupon validation error: {str(e)}", exc_info=True)
        return jsonify({
            'status': 'error',
            'message': 'Error validating coupon'
        }), 500
    finally:
        if 'conn' in locals():
            conn.close()

@app.route('/apply_coupon', methods=['POST'])
@csrf_exempt
def apply_coupon():
    if 'user_id' not in session:
        return jsonify({'status': 'error', 'message': 'Please login first'}), 401

    try:
        data = request.get_json()
        booking_id = data.get('booking_id')
        user_id = session['user_id']
        active_coupon = session.get('active_coupon')

        if not active_coupon:
            return jsonify({'status': 'error', 'message': 'No active coupon found'}), 400

        conn = get_db_connection()
        with conn.cursor() as cursor:
            # Record coupon usage
            cursor.execute("""
                INSERT INTO coupon_usage (coupon_id, user_id, booking_id)
                VALUES (%s, %s, %s)
            """, (active_coupon['id'], user_id, booking_id))

         
            conn.commit()

            # Clear active coupon from session
            session.pop('active_coupon', None)

            return jsonify({
                'status': 'success',
                'message': 'Coupon applied successfully to booking'
            })

    except Exception as e:
        logger.error(f"Apply coupon error: {str(e)}", exc_info=True)
        if 'conn' in locals():
            conn.rollback()
        return jsonify({'status': 'error', 'message': 'Error applying coupon'}), 500
    finally:
        if 'conn' in locals():
            conn.close()

@app.route('/remove_coupon', methods=['POST'])
@csrf_exempt
def remove_coupon():
    if 'user_id' not in session:
        return jsonify({
            'status': 'error',
            'message': 'Please login first'
        }), 401

    try:
        # Ensure request is JSON
        if not request.is_json:
            return jsonify({
                'status': 'error',
                'message': 'Invalid request format. Expected JSON.'
            }), 400

        # Remove coupon from session
        if 'active_coupon' in session:
            session.pop('active_coupon', None)
            session.modified = True  # Ensure session changes are saved

            return jsonify({
                'status': 'success',
                'message': 'Coupon removed successfully'
            })
        else:
            # Return success even if no coupon was found
            return jsonify({
                'status': 'success',
                'message': 'No active coupon found'
            })

    except Exception as e:
        logger.error(f"Error removing coupon: {str(e)}", exc_info=True)
        return jsonify({
            'status': 'error',
            'message': f'Error removing coupon: {str(e)}'
        }), 500

# Add pickup agent routes and functionality
@app.route('/pickup/login', methods=['GET', 'POST'])
def pickup_login():
    if request.method == 'POST':
        email = request.form.get('email')
        password = request.form.get('password')

        conn = get_db_connection()
        if not conn:
            flash('Database connection failed', 'error')
            return render_template('login_signup.html', is_pickup_agent=True)

        cursor = conn.cursor(dictionary=True)
        cursor.execute("SELECT id, email, password_hash, name FROM pickup_agents WHERE email = %s", (email,))
        agent = cursor.fetchone()
        cursor.close()
        conn.close()

        if agent and agent['password_hash']:
            try:
                if bcrypt.checkpw(password.encode('utf-8'), agent['password_hash'].encode('utf-8')):
                    session['agent_id'] = agent['id']
                    session['agent_name'] = agent['name']
                    return jsonify({
                        'status': 'success',
                        'message': 'Login successful',
                        'redirect': url_for('pickup_agent', section='dashboard', agent_id=agent['id'])
                    })
            except Exception as e:
                logger.error(f"Agent password verification error: {str(e)}", exc_info=True)
        else:
            flash('Invalid email or password', 'error')
        
        return render_template('login_signup.html', is_pickup_agent=True)

    return render_template('login_signup.html', is_pickup_agent=True)

# Function to format timedelta to HH:MM string
def format_timedelta(td):
    if not td:
        return 'N/A'
    total_seconds = int(td.total_seconds())
    hours = total_seconds // 3600
    minutes = (total_seconds % 3600) // 60
    return f"{hours:02d}:{minutes:02d}"

# Function to fetch agent data from pickup_agents table
def get_agent_data(agent_id):
    conn = get_db_connection()
    if not conn:
        return None
    cursor = conn.cursor(dictionary=True)  # Use dictionary cursor
    try:
        cursor.execute("""
            SELECT id, professional_id, name, phone, status, service_area, email,
                   (SELECT COUNT(*) FROM sample_collections WHERE agent_id = %s) as total_collections,
                   (SELECT COUNT(*) FROM sample_collections WHERE agent_id = %s AND collection_status = 'Delivered') as completed_collections
            FROM pickup_agents
            WHERE id = %s
        """, (agent_id, agent_id, agent_id))
        agent = cursor.fetchone()
        return agent
    finally:
        cursor.close()
        conn.close()

# Middleware to check if pickup agent is logged in
def pickup_login_required(func):
    def wrapper(*args, **kwargs):
        if 'agent_id' not in session:
            flash('Please login to access this page', 'error')
            return redirect(url_for('pickup_login'))
        return func(*args, **kwargs)
    wrapper.__name__ = func.__name__
    return wrapper

# Main pickup agent dashboard route
@app.route('/pickup/agent/<section>/<int:agent_id>')
@pickup_login_required
def pickup_agent(section, agent_id):
    if session.get('agent_id') != agent_id:
        flash('Unauthorized access', 'error')
        return redirect(url_for('pickup_login'))

    conn = get_db_connection()
    if not conn:
        return "Database connection failed", 500
    cursor = conn.cursor(dictionary=True)

    # Get agent data
    agent = get_agent_data(agent_id)
    if not agent:
        cursor.close()
        conn.close()
        return "Agent not found", 404

    # Get tasks for collections (Pending, Collected, and Delivered)
    cursor.execute("""
        SELECT sc.id, sc.booking_id, sc.collection_status, sc.collection_date,
               u.first_name, u.last_name, b.address_line1, b.city, b.state,
               b.booking_date, b.appointment_time, t.TestName
        FROM sample_collections sc
        JOIN bookings b ON sc.booking_id = b.id
        JOIN user_profiles u ON b.user_id = u.user_id
        JOIN testdetails t ON b.test_id = t.SrNo
        WHERE sc.agent_id = %s AND sc.collection_status IN ('Pending', 'Collected', 'Delivered')
    """, (agent_id,))
    tasks = cursor.fetchall()
    logger.info(f"Found {len(tasks)} tasks for agent {agent_id}: {[{'id': t['id'], 'status': t['collection_status']} for t in tasks]}")

    # Format tasks
    formatted_tasks = []
    for task in tasks:
        formatted_task = {
            'id': task['id'],
            'booking_id': task['booking_id'],
            'collection_status': task['collection_status'],
            'collection_date': task['collection_date'],
            'first_name': task['first_name'],
            'last_name': task['last_name'],
            'address_line1': task['address_line1'],
            'city': task['city'],
            'state': task['state'],
            'booking_date': task['booking_date'],
            'appointment_time': task['appointment_time'],
            'TestName': task['TestName'],
            'customer_name': f"{task['first_name']} {task['last_name']}",
            'formatted_date': task['booking_date'].strftime('%Y-%m-%d') if task['booking_date'] else 'N/A',
            'formatted_time': format_timedelta(task['appointment_time']) if task['appointment_time'] else 'N/A'
        }
        formatted_tasks.append(formatted_task)

    # Get history (Delivered tasks)
    cursor.execute("""
        SELECT sc.id, sc.booking_id, sc.collection_status, sc.collection_date,
               u.first_name, u.last_name, b.address_line1, b.city, b.state,
               b.booking_date, b.appointment_time, t.TestName
        FROM sample_collections sc
        JOIN bookings b ON sc.booking_id = b.id
        JOIN user_profiles u ON b.user_id = u.user_id
        JOIN testdetails t ON b.test_id = t.SrNo
        WHERE sc.agent_id = %s AND sc.collection_status = 'Delivered'
    """, (agent_id,))
    history = cursor.fetchall()

    # Format history
    formatted_history = []
    for item in history:
        formatted_item = {
            'id': item['id'],
            'booking_id': item['booking_id'],
            'collection_status': item['collection_status'],
            'collection_date': item['collection_date'],
            'first_name': item['first_name'],
            'last_name': item['last_name'],
            'address_line1': item['address_line1'],
            'city': item['city'],
            'state': item['state'],
            'booking_date': item['booking_date'],
            'appointment_time': item['appointment_time'],
            'TestName': item['TestName'],
            'customer_name': f"{item['first_name']} {item['last_name']}",
            'formatted_date': item['booking_date'].strftime('%Y-%m-%d') if item['booking_date'] else 'N/A',
            'formatted_time': format_timedelta(item['appointment_time']) if item['appointment_time'] else 'N/A'
        }
        formatted_history.append(formatted_item)

    cursor.close()
    conn.close()

    show_today = section == 'dashboard'

    response = make_response(render_template(
        'pickup.html',
        agent=agent,
        profile=agent,
        tasks=formatted_tasks,
        history=formatted_history,
        active_section=section,
        show_today=show_today
    ))

    # Add cache-busting headers to prevent stale data
    response.headers['Cache-Control'] = 'no-cache, no-store, must-revalidate'
    response.headers['Pragma'] = 'no-cache'
    response.headers['Expires'] = '0'

    return response

# Route to update task status
@app.route('/pickup/update_status/<int:task_id>', methods=['POST'])
@csrf_exempt
@pickup_login_required
def update_pickup_status(task_id):
    logger.info(f"=== UPDATE STATUS REQUEST RECEIVED ===")
    logger.info(f"Task ID: {task_id}")
    logger.info(f"Agent ID: {session.get('agent_id')}")
    logger.info(f"Form data: {dict(request.form)}")
    logger.info(f"Request method: {request.method}")

    conn = None
    cursor = None
    try:
        if 'agent_id' not in session:
            logger.error("No agent_id in session")
            flash('Please log in again', 'error')
            return redirect(url_for('pickup_login'))

        status = request.form.get('status')
        logger.info(f"Attempting to update task {task_id} to status {status} for agent {session['agent_id']}")

        # Validate status
        if status not in ['Collected', 'Delivered']:
            logger.error(f"Invalid status: {status}")
            flash('Invalid status provided', 'error')
            return redirect(url_for('pickup_agent', section='collections', agent_id=session['agent_id']))

        conn = get_db_connection()
        if not conn:
            logger.error("Database connection failed")
            flash('Database connection failed. Please try again.', 'error')
            return redirect(url_for('pickup_agent', section='collections', agent_id=session['agent_id']))

        cursor = conn.cursor()

        # Check if task belongs to agent
        cursor.execute("""
            SELECT id, collection_status
            FROM sample_collections
            WHERE id = %s AND agent_id = %s
        """, (task_id, session['agent_id']))

        task = cursor.fetchone()
        logger.info(f"Task query result: {task}")

        if not task:
            logger.warning(f"Task {task_id} not found for agent {session['agent_id']}")
            flash('Task not found or access denied', 'error')
            return redirect(url_for('pickup_agent', section='collections', agent_id=session['agent_id']))

        # Update status
        cursor.execute("""
            UPDATE sample_collections
            SET collection_status = %s,
                updated_at = NOW()
            WHERE id = %s
        """, (status, task_id))

        affected_rows = cursor.rowcount
        logger.info(f"Update query affected {affected_rows} rows")

        conn.commit()
        logger.info(f"Successfully updated task {task_id} to status {status}")
        flash(f'Sample marked as {status.lower()} successfully', 'success')

        # Add a small delay to ensure database changes are committed
        import time
        time.sleep(0.1)

        redirect_url = url_for('pickup_agent', section='collections', agent_id=session['agent_id'])
        logger.info(f"Redirecting to: {redirect_url}")
        return redirect(redirect_url)

    except Exception as e:
        logger.error(f"Error updating pickup status: {str(e)}", exc_info=True)
        flash('Error updating status. Please try again.', 'error')
        return redirect(url_for('pickup_agent', section='collections', agent_id=session['agent_id']))
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# Route to update agent status
@app.route('/pickup/update_agent_status', methods=['POST'])
@csrf_exempt
@pickup_login_required
def update_pickup_agent_status():
    status = request.form.get('status')
    if status not in ['Available', 'Busy', 'Inactive']:
        return redirect(url_for('pickup_agent', section='dashboard', agent_id=session['agent_id']))

    conn = get_db_connection()
    if not conn:
        return "Database connection failed", 500
    cursor = conn.cursor()
    cursor.execute("""
        UPDATE pickup_agents
        SET status = %s
        WHERE id = %s
    """, (status, session['agent_id']))
    conn.commit()
    cursor.close()
    conn.close()

    return redirect(url_for('pickup_agent', section='dashboard', agent_id=session['agent_id']))

# Test route to verify form submission
@app.route('/pickup/test_form', methods=['GET', 'POST'])
@csrf_exempt
def test_form():
    if request.method == 'POST':
        logger.info(f"TEST FORM RECEIVED: {dict(request.form)}")
        return f"Form received! Data: {dict(request.form)}"

    return '''
    <form method="POST">
        <input type="hidden" name="test" value="working">
        <button type="submit">Test Submit</button>
    </form>
    '''

# Simple test route for pickup status update
@app.route('/pickup/simple_test/<int:task_id>', methods=['GET', 'POST'])
@csrf_exempt
def simple_test_update(task_id):
    if request.method == 'POST':
        logger.info(f"=== SIMPLE TEST UPDATE RECEIVED ===")
        logger.info(f"Task ID: {task_id}")
        logger.info(f"Form data: {dict(request.form)}")
        return f"Update received for task {task_id}! Data: {dict(request.form)}"

    return f'''
    <h2>Test Update for Task {task_id}</h2>
    <form method="POST">
        <input type="hidden" name="status" value="Collected">
        <button type="submit">Test Update Status</button>
    </form>
    '''



# Pickup agent logout route
@app.route('/pickup/logout')
def pickup_logout():
    # Clear all session data
    session.clear()
    
    # Clear any flash messages
    flash('You have been logged out', 'success')
    
    return redirect(url_for('home'))

@app.route('/staff/api/receptionist/dashboard')
@login_required
def staff_dashboard():
    try:
        conn = get_db_connection()
        if not conn:
            return jsonify({"error": "Database connection failed"}), 500
            
        cursor = conn.cursor(dictionary=True)
        
        # Get total appointments
        cursor.execute("SELECT COUNT(*) as count FROM bookings")
        total_appointments = cursor.fetchone()['count']
        
        # Get scheduled pickups
        cursor.execute("SELECT COUNT(*) as count FROM bookings WHERE booking_status = 'scheduled'")
        scheduled_pickups = cursor.fetchone()['count']
        
        # Get pending reports
        cursor.execute("SELECT COUNT(*) as count FROM reports WHERE report_status = 'pending'")
        pending_reports = cursor.fetchone()['count']
        
        cursor.close()
        conn.close()
        
        return jsonify({
            "total_appointments": total_appointments,
            "scheduled_pickups": scheduled_pickups,
            "pending_reports": pending_reports
        })
    except Exception as e:
        return jsonify({"error": str(e)}), 500

# Removed duplicate inject_csrf_token function - already defined at line 128

@app.route('/download_report/<int:report_id>')
@login_required
def download_report(report_id):
    if 'user_id' not in session:
        return jsonify({'status': 'error', 'message': 'Please login first'}), 401

    user_id = session['user_id']
    conn = get_db_connection()
    try:
        with conn.cursor(dictionary=True) as cursor:  # FIX: use dictionary=True
            # Allow download for both Verified and Completed statuses
            cursor.execute("""
                SELECT pr.*, t.TestName 
                FROM patient_report pr
                JOIN bookings b ON pr.booking_id = b.id 
                JOIN testdetails t ON b.test_id = t.SrNo 
                WHERE pr.id = %s AND pr.patient_id = %s AND pr.report_status IN ('Completed', 'Verified')
            """, (report_id, user_id))
            report = cursor.fetchone()

            if not report:
                flash('Report not found or not available for download', 'error')
                return redirect(url_for('dashboard'))

            # Get the report file path
            report_path = report['report_url']
            # If the path is just a filename, prepend upload folder
            upload_folder = os.getenv('UPLOAD_FOLDER', 'uploads')
            if not os.path.isabs(report_path) and not any(sep in report_path for sep in ('/', '\\')):
                report_path = os.path.join(upload_folder, 'reports', report_path)

            # Check if file exists
            if not os.path.exists(report_path):
                flash('Report file not found', 'error')
                return redirect(url_for('dashboard'))

            # Get the filename from the path
            filename = f"{report['TestName']}_{report['barcode']}.pdf"
            # Send the file
            return send_file(
                report_path,
                as_attachment=True,
                download_name=filename,
                mimetype='application/pdf'
            )

    except Exception as e:
        logger.error(f"Error downloading report: {str(e)}", exc_info=True)
        flash('Error downloading report', 'error')
        return redirect(url_for('dashboard'))
    finally:
        conn.close()

@app.route('/doctor/logout')
def doctor_logout():
    logout_user()
    session.clear()
    flash('You have been logged out', 'success')
    return redirect(url_for('home'))

# Security monitoring endpoints
@app.route('/api/v1/security/metrics', methods=['GET'])
@jwt_required
@role_required(['admin'])
@api_error_handler
def api_security_metrics():
    """Get security metrics (admin only)"""
    metrics = security_metrics.get_metrics()
    daily_metrics = security_metrics.get_daily_metrics()

    return jsonify({
        'status': 'success',
        'data': {
            'total_metrics': metrics,
            'daily_metrics': daily_metrics
        },
        'api_version': 'v1'
    })

@app.route('/api/v1/security/report', methods=['GET'])
@jwt_required
@role_required(['admin'])
@api_error_handler
def api_security_report():
    """Generate security report (admin only)"""
    report = security_metrics.generate_security_report()

    return jsonify({
        'status': 'success',
        'data': report,
        'api_version': 'v1'
    })

if __name__ == '__main__':
    port = int(os.getenv('PORT', 7000))
    debug = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'
    app.run(debug=debug, port=port, host='0.0.0.0')